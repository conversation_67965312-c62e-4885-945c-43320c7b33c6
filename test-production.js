#!/usr/bin/env node

/**
 * Test script to validate production NODE_ENV behavior locally
 * This script helps debug the NODE_ENV issue in server/index.ts
 */

import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 Testing NODE_ENV behavior for production build...\n');

// Test 1: Check current NODE_ENV
console.log('1. Current environment variables:');
console.log(`   NODE_ENV: ${process.env.NODE_ENV || 'undefined'}`);
console.log(`   PORT: ${process.env.PORT || 'undefined'}`);
console.log('');

// Test 2: Build the application
console.log('2. Building the application...');
const buildProcess = spawn('pnpm', ['build'], {
  stdio: 'inherit',
  cwd: process.cwd()
});

buildProcess.on('close', (code) => {
  if (code !== 0) {
    console.error('❌ Build failed with code:', code);
    process.exit(1);
  }

  console.log('✅ Build completed successfully\n');

  // Test 2.5: Verify build output
  console.log('2.5. Verifying build output...');
  const distPath = path.join(process.cwd(), 'dist/public');
  const indexPath = path.join(distPath, 'index.html');

  if (fs.existsSync(distPath)) {
    console.log('   ✅ dist/public directory exists');
    if (fs.existsSync(indexPath)) {
      console.log('   ✅ dist/public/index.html exists');
    } else {
      console.log('   ❌ dist/public/index.html missing');
    }

    const files = fs.readdirSync(distPath);
    console.log(`   📁 Files in dist/public: ${files.join(', ')}`);
  } else {
    console.log('   ❌ dist/public directory missing');
  }
  console.log('');

  // Test 3: Start server with production NODE_ENV
  console.log('3. Starting server with NODE_ENV=production...');
  console.log('   🔍 Watch for debug logs showing environment details');
  console.log('   🔍 Watch for "Serving static files from:" log message');
  console.log('   🌐 Server should be available at http://localhost:8594');
  console.log('   ⏹️  Press Ctrl+C to stop the server\n');

  const serverProcess = spawn('pnpm', ['start'], {
    stdio: 'inherit',
    cwd: process.cwd(),
    env: {
      ...process.env,
      NODE_ENV: 'production'
    }
  });

  // Handle Ctrl+C gracefully
  process.on('SIGINT', () => {
    console.log('\n🛑 Stopping server...');
    serverProcess.kill('SIGINT');
    process.exit(0);
  });

  serverProcess.on('close', (code) => {
    console.log(`Server exited with code: ${code}`);
    process.exit(code);
  });
});

buildProcess.on('error', (err) => {
  console.error('❌ Build process error:', err);
  process.exit(1);
});
