import { createInsertSchema } from 'drizzle-zod';
import { oshaAuditTrail, oshaCompanyInformation } from './schema';
import { InferSelectModel } from 'drizzle-orm';
import { z } from 'zod';

export const RequestInfoSchema = z.object({
  ipAddress: z.string().ip('Invalid IP address').optional(),
  userAgent: z.string().min(1, 'User agent is required').optional(),
});

const OshaCompanyInformationValidations = {
  companyEIN: z
    .string()
    .length(9, 'EIN must be exactly 9 digits')
    .regex(/^\d{9}$/, 'EIN must be exactly 9 digits'),
  companyAnnualAverageNumberOfEmployees: z.coerce.number().positive('Number of employees must be positive'),
  companyTotalHoursWorked: z.coerce.number().positive('Total hours worked must be positive'),
  year: z
    .number()
    .int()
    .min(1900)
    .max(new Date().getFullYear() + 1, 'Year must be valid'),
  companyNAICSCode: z.coerce.number().int().positive('NAICS code must be a positive integer').min(10).max(999999),
  companyName: z.string().min(1, 'Company name is required'),
  companyFacilityId: z.string().min(1, 'Facility ID is required'),
};

// OSHA Company Information Schema with validation
export const UpsertOshaCompanyInformationSchema = createInsertSchema(
  oshaCompanyInformation,
  OshaCompanyInformationValidations,
)
  .omit({
    id: true,
    upkeepCompanyId: true,
    createdBy: true,
    createdAt: true,
    updatedAt: true,
    archivedAt: true,
    archivedBy: true,
  })
  .and(RequestInfoSchema);

export type UpsertOshaCompanyInformation = z.infer<typeof UpsertOshaCompanyInformationSchema>;
export type OshaCompanyInformation = InferSelectModel<typeof oshaCompanyInformation>;

const OshaSummaryExecutiveCertificationValidations = {
  executiveName: z.string().min(1, 'Executive name is required'),
  executiveTitle: z.string().min(1, 'Executive title is required'),
  dateCertified: z.coerce.date(),
  digitalSignature: z.string().min(1, 'Digital signature is required'),
};

export const OshaSummaryExecutiveCertificationSchema = createInsertSchema(
  oshaCompanyInformation,
  OshaSummaryExecutiveCertificationValidations,
)
  .omit({
    id: true,
    upkeepCompanyId: true,
    createdBy: true,
    createdAt: true,
    updatedAt: true,
    archivedAt: true,
    archivedBy: true,
    archived: true,
    companyName: true,
    companyFacilityId: true,
    companyNAICSCode: true,
    companyEIN: true,
    companyAnnualAverageNumberOfEmployees: true,
    companyTotalHoursWorked: true,
  })
  .and(RequestInfoSchema)
  .and(
    z.object({
      summaryId: z.string().cuid2(),
    }),
  );

export type OshaSummaryExecutiveCertification = z.infer<typeof OshaSummaryExecutiveCertificationSchema>;

export const GetEstablishInformationByYearSchema = z.object({
  year: z.number(),
});

export type GetEstablishInformationByYear = z.infer<typeof GetEstablishInformationByYearSchema>;

export const OshaAuditTrailCreateSchema = createInsertSchema(oshaAuditTrail);

export const OshaAuditTrailSchema = OshaAuditTrailCreateSchema.omit({
  id: true,
  upkeepCompanyId: true,
  details: true,
  createdAt: true,
  createdBy: true,
});

export const OshaAuditTrailProcedureSchema = OshaAuditTrailSchema.omit({
  ipAddress: true,
  userAgent: true,
});

export type OshaAuditTrail = z.infer<typeof OshaAuditTrailSchema>;
