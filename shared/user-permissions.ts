import { z } from 'zod';

export const USER_ACCOUNT_TYPES = {
  ADMIN: 'admin',
  TECHNICIAN: 'user',
} as const;

export const UPKEEP_USER_ACCOUNT_TYPES = {
  ADMIN: '1',
  TECHNICIAN: '2',
  VIEW_ONLY: '3',
  REQUESTOR: '4',
  LIMITED_TECHNICIAN: '5',
  VENDOR: '6',
} as const;

export const MODULES = {
  EHS_INCIDENT: 'ehs-incident',
  EHS_CAPA: 'ehs-capa',
  EHS_ACCESS_POINT: 'ehs-access-point',
  EHS_OSHA_REPORTS: 'ehs-osha-reports',
} as const;

export const ALLOWED_ACTIONS = {
  CREATE: 'create',
  VIEW: 'view',
  EDIT: 'edit',
  DELETE: 'delete',
} as const;

export const PERMISSION_LEVELS = {
  FULL: 'full',
  PARTIAL: 'partial',
  NONE: 'none',
} as const;

/**
 * Permission matrix - cleaner, more maintainable structure
 * Easy to see differences between user types at a glance
 */
export const PERMISSION_MATRIX = {
  [MODULES.EHS_ACCESS_POINT]: {
    [ALLOWED_ACTIONS.VIEW]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.CREATE]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.EDIT]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
    },
  },
  [MODULES.EHS_INCIDENT]: {
    [ALLOWED_ACTIONS.CREATE]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.FULL,
    },
    [ALLOWED_ACTIONS.VIEW]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.PARTIAL,
    },
    [ALLOWED_ACTIONS.EDIT]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.PARTIAL,
    },
  },
  [MODULES.EHS_CAPA]: {
    [ALLOWED_ACTIONS.VIEW]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.PARTIAL,
    },
    [ALLOWED_ACTIONS.CREATE]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.EDIT]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.PARTIAL,
    },
  },
  [MODULES.EHS_OSHA_REPORTS]: {
    [ALLOWED_ACTIONS.CREATE]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.VIEW]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.EDIT]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
    },
  },
} as const;

export const AllowedActionsSchema = z.enum(Object.values(ALLOWED_ACTIONS) as [string, ...string[]]);
export const PermissionLevelsSchema = z.enum(Object.values(PERMISSION_LEVELS) as [string, ...string[]]);
export const ModulesSchema = z.enum(Object.values(MODULES) as [string, ...string[]]);

export const UserPermissionSchema = z.record(ModulesSchema, z.record(AllowedActionsSchema, PermissionLevelsSchema));

export type AllowedActions = z.infer<typeof AllowedActionsSchema>;
export type UserPermission = z.infer<typeof UserPermissionSchema>;
export type Modules = z.infer<typeof ModulesSchema>;

export type PermissionLevel = z.infer<typeof PermissionLevelsSchema>;

/**
 * Generate permission arrays from matrix
 */
export const generatePermissions = (
  userType: typeof USER_ACCOUNT_TYPES.ADMIN | typeof USER_ACCOUNT_TYPES.TECHNICIAN,
): Record<keyof typeof PERMISSION_MATRIX, Record<AllowedActions, PermissionLevel>> => {
  const permissions: Record<keyof typeof PERMISSION_MATRIX, Record<AllowedActions, PermissionLevel>> = {} as Record<
    keyof typeof PERMISSION_MATRIX,
    Record<AllowedActions, PermissionLevel>
  >;

  for (const [moduleName, actions] of Object.entries(PERMISSION_MATRIX)) {
    permissions[moduleName as keyof typeof PERMISSION_MATRIX] = {} as Record<AllowedActions, PermissionLevel>;

    for (const [action, levels] of Object.entries(actions)) {
      permissions[moduleName as keyof typeof PERMISSION_MATRIX][action as AllowedActions] = levels[
        userType
      ] as PermissionLevel;
    }
  }

  return permissions;
};

export const permissions = {
  [USER_ACCOUNT_TYPES.ADMIN]: generatePermissions(USER_ACCOUNT_TYPES.ADMIN),
  [USER_ACCOUNT_TYPES.TECHNICIAN]: generatePermissions(USER_ACCOUNT_TYPES.TECHNICIAN),
};
