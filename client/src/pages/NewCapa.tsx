import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { CapaSuccessModal } from '@/components/capas/create/capa-success-modal';
import { AnalyzingLoading } from '@/components/composite/analyzing-loading';
import { AsyncAssetSelect } from '@/components/composite/async-asset-select';
import { AsyncIncidentsSelect } from '@/components/composite/async-incidents-select';
import { AsyncLocationSelect } from '@/components/composite/async-location-select';
import { AsyncUserSelect } from '@/components/composite/async-user-select';
import { MediaUpload } from '@/components/composite/media-upload';
import { VoiceInput } from '@/components/composite/voice-input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DateTimePicker } from '@/components/ui/date-time-picker';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useAnalytics } from '@/hooks/use-analytics';
import { usePermissions } from '@/hooks/use-permissions';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { capaPriorityEnum, capaTagsEnum, capaTypeEnum, rcaMethodEnum, rootCauseEnum, statusEnum } from '@shared/schema';
import {
  CAPA_TAGS_MAP,
  CAPA_TYPE_MAP,
  CreateCapasFormSchema,
  RCA_METHOD_MAP,
  ROOT_CAUSE_MAP,
} from '@shared/schema.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import axios from 'axios';
import { Check, Info, Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { Redirect, useLocation, useSearchParams } from 'wouter';
import { z } from 'zod';

const FormSchema = CreateCapasFormSchema;

export default function NewCapa() {
  const [_, navigate] = useLocation();
  const { track } = useAnalytics();
  const { hasPermission } = usePermissions();

  // States for UI
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [autoFilledFields, setAutoFilledFields] = useState<string[]>([]);
  const [showSuccessModal, setShowSuccessModal] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [createdCapaId, setCreatedCapaId] = useState<string>();

  const [isVoiceUsed, setIsVoiceUsed] = useState(false);
  const [aiSummary, setAiSummary] = useState<string>('');

  const [searchParams] = useSearchParams();
  const utils = trpc.useUtils();

  const linkedEventId = searchParams.get('eventId') ?? null;

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      title: '',
      type: capaTypeEnum.enumValues[0],
      eventId: linkedEventId,
      rcaMethod: rcaMethodEnum.enumValues[0],
      rcaFindings: '',
      rootCause: rootCauseEnum.enumValues[0],
      otherRootCause: '',
      aiSuggestedAction: '',
      actionsToAddress: '',
      ownerId: undefined,
      dueDate: undefined,
      priority: capaPriorityEnum.enumValues[0],
      tags: [],
      privateToAdmins: false,
      status: statusEnum.enumValues[0],
      teamMembersToNotify: [],
    },
    mode: 'onSubmit',
  });

  const { data: event } = trpc.incident.getById.useQuery(
    {
      id: linkedEventId!,
    },
    {
      enabled: !!linkedEventId,
    },
  );

  const { mutateAsync: getPresignedUrl } = trpc.file.getPresignedUrl.useMutation();

  const { mutateAsync: updateFile } = trpc.file.update.useMutation();

  const { mutateAsync: createCapa } = trpc.capa.create.useMutation({
    onSuccess: () => {
      utils.incident.getById.invalidate({ id: linkedEventId! });
      utils.capa.list.invalidate();
    },
  });

  const { mutateAsync: analyze, isPending: isAnalyzing } = trpc.ai.analyzeCapa.useMutation({
    onSuccess: (data) => {
      console.log('Analysis complete', data);
      toast.success('Analysis complete', {
        description: 'We have analyzed your root cause analysis and filled out the form for you.',
      });
    },
    onError: (error) => {
      console.error('Error analyzing root cause analysis', error);
      toast.error('Error analyzing root cause analysis', {
        description: 'There was a problem analyzing your root cause analysis. Please try again.',
      });
    },
  });

  // Track form view and create initiated on component mount
  useEffect(() => {
    // Determine source based on URL params or referrer - inline logic
    const getCapaSource = (): 'global_create' | 'event_detail_page' | 'event_log_row_action' => {
      const search = window.location?.search || '';
      const eventId = search.includes('eventId=');

      if (eventId) {
        if (document.referrer.includes('/incidents/')) {
          return 'event_detail_page';
        } else if (document.referrer.includes('/incidents')) {
          return 'event_log_row_action';
        }
      }

      return 'global_create';
    };

    const source = getCapaSource();

    // Track create initiated
    track(ANALYTICS_EVENTS.CAPA.CREATE_INITIATED, {
      source,
    });

    // Track form viewed
    track(ANALYTICS_EVENTS.CAPA.FORM_VIEWED, {
      source,
    });
  }, []);

  useEffect(() => {
    if (form.watch('locationId')) {
      form.setValue('assetId', null);
      utils.asset.search.invalidate();
    }
  }, [form.watch('locationId')]);

  useEffect(() => {
    if (linkedEventId && event) {
      // Update form with actual safety event data
      form.setValue('title', `CAPA for: ${event.title}`);
      form.setValue('eventId', linkedEventId);

      // Pre-fill location from safety event
      if (event.location) {
        form.setValue('locationId', event.location.id);
      }

      // Set appropriate priority based on hazard category
      if (event.category && ['chemical', 'fire', 'electrical'].includes(event.category)) {
        form.setValue('priority', 'high');
      }

      // Set default due date (2 weeks from now)
      const dueDate = new Date();
      dueDate.setDate(dueDate.getDate() + 14);
      form.setValue('dueDate', dueDate);
    }
  }, [event, linkedEventId]);

  // Separate useEffect to handle setting asset after location is set
  useEffect(() => {
    if (event && event.assetIds?.length === 1) {
      form.setValue('assetId', event.assetIds[0]);
    }
  }, [event, form.watch('locationId')]);

  // Handle the voice analysis results from the VoiceInput component
  const handleVoiceAnalysis = async (text: string) => {
    setIsVoiceUsed(true);
    setAutoFilledFields([]); // Clear any previous animations

    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    const analysis = await analyze({
      text,
      timezone,
    });

    // Store the AI summary
    if (analysis?.summary) {
      setAiSummary(analysis.summary);
    }

    // Animate each field separately with staggered timing
    const animateField = (
      field: keyof z.infer<typeof FormSchema>,
      value: string | Date | undefined | null | string[],
      delay: number,
    ) => {
      setTimeout(() => {
        form.setValue(field, value);
        setAutoFilledFields((prev) => [...prev, field]);
      }, delay);
    };

    const baseDelay = 300;
    let currentDelay = 0;

    if (!linkedEventId) {
      animateField('title', analysis?.title, currentDelay);
      currentDelay += baseDelay;
    }

    if (analysis.rcaMethod) {
      animateField('rcaMethod', analysis.rcaMethod, currentDelay);
      currentDelay += baseDelay;
    }

    animateField('rcaFindings', analysis.rcaFindings, currentDelay);
    currentDelay += baseDelay;

    animateField('actionsToAddress', analysis.actionsToAddress, currentDelay);
    currentDelay += baseDelay;

    console.log('analysis.rootCause', analysis.rootCause);

    animateField('rootCause', analysis.rootCause, currentDelay);
    currentDelay += baseDelay;

    animateField('type', analysis?.type, currentDelay);
    currentDelay += baseDelay;

    if (analysis.otherRootCause && analysis.rootCause === 'other') {
      animateField('otherRootCause', analysis?.otherRootCause, currentDelay);
      currentDelay += baseDelay;
    }

    animateField('priority', analysis.priority, currentDelay);
    currentDelay += baseDelay;

    if (analysis.tags) {
      animateField('tags', analysis?.tags, currentDelay);
      setSelectedTags(analysis?.tags);
      currentDelay += baseDelay;
    }

    if (analysis.dueDate) {
      animateField('dueDate', analysis?.dueDate, currentDelay);
      currentDelay += baseDelay;
    }

    toast.success('✨ AI Analysis Complete', {
      description:
        'Form has been populated based on your voice description. Feel free to edit any field before submitting.',
    });
  };

  // Handle tag selection
  const handleTagToggle = (tag: string) => {
    setSelectedTags((prev) => {
      const isSelected = prev.includes(tag);
      if (isSelected) {
        // Remove tag
        const newTags = prev.filter((id) => id !== tag);
        form.setValue('tags', newTags.length > 0 ? newTags : null);
        return newTags;
      } else {
        // Add tag
        const newTags = [...prev, tag];
        form.setValue('tags', newTags);
        return newTags;
      }
    });
  };

  // Handle creating another CAPA
  const handleCreateAnother = () => {
    form.reset();
    setSelectedTags([]);
    setAutoFilledFields([]);
  };

  // Form submission handler
  const onSubmit = async (values: z.infer<typeof FormSchema>) => {
    setIsSubmitting(true);

    try {
      const createdCapa = await createCapa({ ...values, attachments: undefined, summary: aiSummary });

      // Track CAPA creation
      track(ANALYTICS_EVENTS.CAPA.FORM_SUBMITTED, {
        capa_id: createdCapa?.id,
        event_id: values.eventId || undefined,
        capa_type: values.type === 'corrective' ? 'Corrective' : 'Preventive',
        location: values.locationId || '',
        owner_id: values.ownerId || '',
        priority: values.priority || '',
        tags: values.tags || [],
        is_private_to_admins: values.privateToAdmins || false,
        media_attached_count: values.attachments?.length || 0,
        is_ai_assisted: isVoiceUsed,
      });

      // Track CAPA created from incident (if linked to an incident)
      if (values.eventId) {
        track(ANALYTICS_EVENTS.EVENT.CAPA_CREATED_FROM_EVENT, {
          event_id: values.eventId,
          capa_id: createdCapa?.id,
        });
      }

      setCreatedCapaId(createdCapa?.id);

      for (const item of values.attachments ?? []) {
        const result = await getPresignedUrl({
          fileName: item.name,
          fileSize: item.size,
          mimeType: item.type,
          entityType: 'capa',
          entityId: createdCapa?.id,
        });

        try {
          await axios.put(result.presignedUrl, item.file, {
            headers: {
              'Content-Type': result.file?.mimeType,
            },
          });

          if (!result?.file) {
            throw new Error('Error uploading file');
          } else {
            await updateFile({
              id: result.file.id,
              s3Key: result.file.s3Key,
              status: 'completed',
            });
          }
        } catch (error) {
          console.error('Error uploading file', error);
          toast.success('Error uploading file', {
            description: 'There was a problem uploading your file. Please try again.',
          });
        }
      }

      form.reset();

      // Clear any auto-filled fields
      setAutoFilledFields([]);

      toast.success('CAPA created', {
        description: 'Your CAPA has been created successfully',
      });

      // Scroll to top of the page
      window.scrollTo({ top: 0, behavior: 'smooth' });

      // Show the success modal and let user decide when to close it
      setShowSuccessModal(true);
    } catch (error) {
      console.error('Error submitting root cause analysis', error);
      // Track form validation failed
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      track(ANALYTICS_EVENTS.CAPA.FORM_VALIDATION_FAILED, {
        validation_errors: [errorMessage],
      });
      toast.error('Error reporting root cause analysis', {
        description: 'There was a problem submitting your root cause analysis. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.CREATE)) {
    return <Redirect to={ROUTES.CAPA_LIST} />;
  }

  return (
    <div className="max-w-[960px] mx-auto px-4 sm:px-6 md:px-8 lg:px-10 py-6 sm:py-10">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-4">
        <h1 className="text-2xl font-bold text-primary-600">Create CAPA</h1>
        {linkedEventId && event && <div className="text-sm text-gray-500">Linked to Safety Event: {event.title}</div>}
      </div>

      {/* CAPA Form */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="space-y-6 pt-5">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Title <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Brief title for this CAPA" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* CAPA Type and Linked Incident - Responsive layout */}
            <div className="grid grid-cols-1 sm:grid-cols-12 gap-4">
              <div className="sm:col-span-4">
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        CAPA Type <span className="text-red-500">*</span>
                      </FormLabel>
                      <Select onValueChange={field.onChange} value={field.value || ''}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {capaTypeEnum.enumValues.map((type) => (
                            <SelectItem key={type} value={type}>
                              {CAPA_TYPE_MAP[type]}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>The type of action required</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Linked Incident - Mobile Optimized Dropdown */}
              <div className="sm:col-span-8">
                <FormField
                  control={form.control}
                  name="eventId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Link to Safety Event</FormLabel>
                      <AsyncIncidentsSelect
                        onChange={field.onChange}
                        value={field.value}
                        placeholder="Search and select a safety event..."
                        mustIncludeObjectIds={linkedEventId ? [linkedEventId] : undefined}
                      />
                      <FormDescription>
                        {field.value || linkedEventId ? (
                          <span className="text-green-600 flex items-center gap-1">
                            <Check className="size-4" />
                            This CAPA is linked to a safety event
                          </span>
                        ) : (
                          'Optional: Link this CAPA to a safety event. Linked CAPAs will show on the safety event record.'
                        )}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Location and Asset - Responsive layout */}
            <div className="grid grid-cols-1 sm:grid-cols-12 gap-4">
              <div className="sm:col-span-6">
                <FormField
                  control={form.control}
                  name="locationId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Location</FormLabel>
                      <FormControl>
                        <AsyncLocationSelect
                          placeholder="Select a location"
                          {...field}
                          value={field.value}
                          onChange={field.onChange}
                          mustIncludeObjectIds={event?.locationId ? [event.locationId] : undefined}
                        />
                      </FormControl>
                      <FormDescription>Specific area where the CAPA will be implemented</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="sm:col-span-6">
                <FormField
                  control={form.control}
                  name="assetId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Asset (Optional)</FormLabel>
                      <FormControl>
                        <AsyncAssetSelect
                          placeholder="Select an asset"
                          {...field}
                          value={field.value}
                          onChange={field.onChange}
                          locationId={form.watch('locationId') ?? undefined}
                        />
                      </FormControl>
                      <FormDescription>Specific equipment or asset related to this CAPA</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Root Cause Analysis Method - New section */}
            <Card className="border-blue-100 shadow-none mb-4">
              <CardHeader>
                <CardTitle className="text-md font-medium flex items-center gap-2">
                  <Info className="size-4" />
                  Root Cause Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-3">
                {/* AI Assist Voice Input for RCA Section */}
                <div className="p-3 bg-blue-50 rounded-lg border border-blue-100 flex flex-col gap-2">
                  <p className="text-sm text-blue-700">
                    Use your voice to describe the safety event investigation, the RCA method used, and the proposed
                    actions to address the root cause(s).
                  </p>
                  <VoiceInput onAnalysisComplete={handleVoiceAnalysis} isPublic={false} isLoading={isAnalyzing} />
                  {/* AI Analysis Loading */}
                  {isAnalyzing && <AnalyzingLoading />}
                </div>

                <div className="my-4">
                  <FormField
                    control={form.control}
                    name="rcaMethod"
                    render={({ field }) => (
                      <FormItem className={autoFilledFields.includes('rcaMethod') ? 'relative' : ''}>
                        <FormLabel>RCA Method Used</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value || ''}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select analysis method" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {rcaMethodEnum.enumValues.map((method) => (
                              <SelectItem key={method} value={method}>
                                {RCA_METHOD_MAP[method]}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>Choose the method used to analyze the root cause</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* RCA Findings - New field */}
                <FormField
                  control={form.control}
                  name="rcaFindings"
                  render={({ field }) => (
                    <FormItem className={autoFilledFields.includes('rcaFindings') ? 'relative' : ''}>
                      {autoFilledFields.includes('rcaFindings') && (
                        <div className="absolute -inset-1 rounded-md bg-indigo-50/50 border border-indigo-200 -z-10" />
                      )}
                      <FormLabel>
                        RCA Findings & Conclusion
                        <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          {...field}
                          placeholder="Document your findings and root cause analysis conclusion here. If using 5 Whys, include all questions and answers."
                          className="min-h-[120px]"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Root Cause Category moved into RCA section and renamed */}
                <div className="mt-4">
                  <FormField
                    control={form.control}
                    name="rootCause"
                    render={({ field }) => (
                      <FormItem className={autoFilledFields.includes('rootCause') ? 'relative' : ''}>
                        {autoFilledFields.includes('rootCause') && (
                          <div className="absolute -inset-1 rounded-md bg-indigo-50/50 border border-indigo-200 -z-10" />
                        )}
                        <FormLabel>Identified Root Cause Category</FormLabel>
                        <Select
                          onValueChange={(value) => {
                            field.onChange(value);
                            // Clear other root cause when not "other"
                            if (value !== 'other') {
                              form.setValue('otherRootCause', null);
                            }
                          }}
                          value={field.value || ''}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select root cause category" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {rootCauseEnum.enumValues.map((cause) => (
                              <SelectItem key={cause} value={cause}>
                                {ROOT_CAUSE_MAP[cause]}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Select the primary category for the root cause identified in your analysis
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Show "Other" input when "other" is selected */}
                  {form.watch('rootCause') === 'other' && (
                    <FormField
                      control={form.control}
                      name="otherRootCause"
                      render={({ field }) => (
                        <FormItem className="mt-2">
                          <FormLabel>Specify Other Root Cause</FormLabel>
                          <FormControl>
                            <Input
                              value={(field.value as string) || ''}
                              onChange={field.onChange}
                              onBlur={field.onBlur}
                              name={field.name}
                              ref={field.ref}
                              placeholder="Describe the root cause"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Actions to Address - Renamed from Recommended Action */}
            <FormField
              control={form.control}
              name="actionsToAddress"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Proposed Actions
                    <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Based on the RCA, what specific corrective and preventive actions are planned to eliminate the root cause and prevent recurrence?"
                      className="min-h-[120px]"
                      value={field.value || ''}
                    />
                  </FormControl>
                  <FormDescription>Include both immediate actions and long-term preventive measures</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Attachments Section - Moved after Proposed Actions */}
            <div className="pt-2 pb-3">
              <div className="mb-2">
                <MediaUpload
                  maxFiles={5}
                  maxSize={20}
                  className="bg-white"
                  files={form.watch('attachments') ?? []}
                  setFiles={(tFiles) => {
                    form.setValue('attachments', tFiles);
                  }}
                />
              </div>
            </div>

            {/* Owner, Due Date, and Priority - Responsive Layout */}
            <div className="flex flex-col md:flex-row md:items-start gap-4">
              {/* Owner Select */}
              <FormField
                control={form.control}
                name="ownerId"
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormLabel>
                      Owner <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <AsyncUserSelect placeholder="Select an owner" {...field} value={field.value} multiple={false} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Due Date */}
              <FormField
                control={form.control}
                name="dueDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Due Date</FormLabel>
                    <FormControl>
                      <DateTimePicker
                        selected={field.value ?? undefined}
                        onSelect={field.onChange}
                        disabled={{
                          before: new Date(),
                        }}
                        onlyDate
                        placeholder="Select due date"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Priority Radio Select */}
              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormLabel>Priority</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="flex flex-col sm:flex-row sm:space-x-4 sm:mt-2"
                      >
                        <FormItem className="flex items-center">
                          <FormControl>
                            <RadioGroupItem value="low" />
                          </FormControl>
                          <FormLabel className="font-normal cursor-pointer">Low</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center">
                          <FormControl>
                            <RadioGroupItem value="medium" />
                          </FormControl>
                          <FormLabel className="font-normal cursor-pointer">Medium</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center">
                          <FormControl>
                            <RadioGroupItem value="high" />
                          </FormControl>
                          <FormLabel className="font-normal cursor-pointer">High</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Tags - Responsive Grid */}
            <div>
              <FormLabel>Tags</FormLabel>
              <div className={`mt-2 flex flex-wrap gap-2 }`}>
                {capaTagsEnum.enumValues.map((tag) => (
                  <div
                    key={tag}
                    className={`inline-flex items-center px-3 py-1 rounded-full text-sm cursor-pointer transition-colors ${
                      selectedTags.includes(tag)
                        ? 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                        : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                    }`}
                    onClick={() => handleTagToggle(tag)}
                  >
                    {selectedTags.includes(tag) && <Check className="size-4" />}
                    {CAPA_TAGS_MAP[tag]}
                  </div>
                ))}
              </div>
              <div className="mt-1 text-sm text-gray-500">
                Select all applicable tags to help with searching and filtering
              </div>
            </div>

            {/* Private to Admins Checkbox */}
            {/* TODO: REMOVED FROM V1 */}
            {/* <FormField
              control={form.control}
              name="privateToAdmins"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox checked={field.value || false} onCheckedChange={field.onChange} />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Private to Administrators</FormLabel>
                    <FormDescription>If checked, this CAPA will only be visible to administrators.</FormDescription>
                  </div>
                </FormItem>
              )}
            /> */}

            {/* Team Members to Notify */}
            <FormField
              control={form.control}
              name="teamMembersToNotify"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>Team Members to Notify</FormLabel>
                  </div>
                  <FormControl>
                    <AsyncUserSelect
                      placeholder="Select team members to notify"
                      {...field}
                      value={field.value}
                      multiple={true}
                    />
                  </FormControl>
                  <FormDescription>Select team members who should be notified about this CAPA update.</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Submit Button */}
            <div className="flex flex-col sm:flex-row justify-end mt-8 space-y-2 sm:space-y-0 sm:space-x-2">
              <Button type="button" variant="outline" onClick={() => navigate(ROUTES.CAPA_LIST)}>
                Cancel
              </Button>

              {/* Primary Submit Button */}
              <Button type="submit" disabled={isSubmitting} className="min-w-[100px]">
                {isSubmitting ? <Loader2 className="size-4 animate-spin" /> : null}
                {isSubmitting ? 'Creating...' : 'Create CAPA'}
              </Button>
            </div>
          </div>
        </form>
      </Form>

      {/* Success Modal with confetti */}
      <CapaSuccessModal
        open={showSuccessModal}
        onOpenChange={setShowSuccessModal}
        onCreateAnother={handleCreateAnother}
        capaId={createdCapaId}
      />
    </div>
  );
}
