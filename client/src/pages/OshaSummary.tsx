import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { YearSelect } from '@/components/composite/year-select';
import { CasesSummary } from '@/components/osha-summary/cases-summary';
import { CompanyExecutiveCertificationDetails } from '@/components/osha-summary/company-executive-certification-details';
import { CompanyExecutiveCertificationForm } from '@/components/osha-summary/company-executive-certification-form';
import { EstablishmentInformationDetails } from '@/components/osha-summary/establishment-information-details';
import { EstablishmentInformationForm } from '@/components/osha-summary/establishment-information-form';
import { OshaSummaryConfirmationDialog } from '@/components/osha-summary/osha-summary-confirmation-dialog';
import { OshaSummarySuccessDialog } from '@/components/osha-summary/osha-summary-success-dialog';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { generateOshaSummaryReport } from '@/lib/download-osha-summary-report';
import { generateOshaSummaryPdf } from '@/lib/generate-osha-summary-pdf';
import { trpc } from '@/providers/trpc';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { Archive, Calendar, Download, FileText } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { useLocation } from 'wouter';

export default function OshaSummary() {
  const [_, navigate] = useLocation();

  const [year, setYear] = useState<number>(new Date().getFullYear());
  const [showArchiveConfirmDialog, setShowArchiveConfirmDialog] = useState(false);
  const [showOshaSummaryConfirmDialog, setShowOshaSummaryConfirmDialog] = useState(false);
  const [showOshaSummarySuccessDialog, setShowOshaSummarySuccessDialog] = useState(false);

  const [isEditingCompanyInformation, setIsEditingCompanyInformation] = useState(false);
  const [isEditingCompanyExecutiveCertification, setIsEditingCompanyExecutiveCertification] = useState(false);

  const { data: establishmentInfo, error: establishmentInfoError } =
    trpc.oshaSummary.getEstablishmentInformationByYear.useQuery({
      year: year,
    });

  const { data: summary, error: summaryError } = trpc.oshaSummary.getOshaCasesSummary.useQuery({
    year,
  });

  useEffect(() => {
    if (establishmentInfoError?.data?.code === 'FORBIDDEN' || summaryError?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [establishmentInfoError, summaryError]);

  const { mutateAsync: createOshaAuditTrail } = trpc.oshaAuditTrail.create.useMutation();

  const handleExportPDF = () => {
    if (!summary || !establishmentInfo) {
      toast.error('Failed to generate PDF', {
        description: 'Please try again later',
      });
      return;
    }

    generateOshaSummaryPdf({
      summary,
      establishmentInfo,
      year,
    });
  };

  const onDownload = async () => {
    await createOshaAuditTrail({
      entityId: establishmentInfo!.id,
      entityType: 'osha_report',
      action: 'downloaded',
    });

    setShowOshaSummarySuccessDialog(true);
  };

  const onGenerateAndDownloadReport = async () => {
    setShowOshaSummaryConfirmDialog(false);

    if (!establishmentInfo || !summary) {
      toast.error('Failed to generate report', {
        description: 'Please try again later',
      });
      return;
    }

    generateOshaSummaryReport({
      establishmentInfo,
      summary,
      onDownload,
    });
  };

  return (
    <div className="container mx-auto px-4 py-6 max-w-7xl">
      <ArchiveConfirmationDialog
        archived={establishmentInfo?.archived ?? false}
        showDialog={showArchiveConfirmDialog}
        setShowDialog={setShowArchiveConfirmDialog}
        entityId={establishmentInfo?.id ?? ''}
        entityType="oshaSummary"
      />

      <OshaSummaryConfirmationDialog
        showDialog={showOshaSummaryConfirmDialog}
        setShowDialog={setShowOshaSummaryConfirmDialog}
        year={year}
        onGenerateAndDownloadReport={onGenerateAndDownloadReport}
      />

      <OshaSummarySuccessDialog
        showDialog={showOshaSummarySuccessDialog}
        setShowDialog={setShowOshaSummarySuccessDialog}
        year={year}
      />

      {/* Header Section */}
      <div className="mb-8">
        {/* Title and Icon */}

        {establishmentInfo?.archived && (
          <Alert variant="destructive" className="mb-6 border-red-200 bg-red-50">
            <AlertTitle>
              <div className="flex items-center gap-2">
                <Archive className="h-4 w-4 text-red-600" />
                <span>ARCHIVED - This year's OSHA logs are archived and read-only</span>
              </div>
            </AlertTitle>
            <AlertDescription>
              These records cannot be modified. To edit current year data, please navigate back to the main view.
              <Button variant="outline" size="sm" onClick={() => setShowArchiveConfirmDialog(true)} className="mt-1">
                Restore Year
              </Button>
            </AlertDescription>
          </Alert>
        )}
        <div className="flex items-center mb-6">
          <Calendar className="h-8 w-8 text-green-600 mr-3 bg-green-100 rounded p-1 flex-shrink-0" />
          <h1 className="text-2xl font-bold tracking-tight text-gray-900">
            OSHA Form 300A Summary of Work-Related Injuries and Illnesses
          </h1>
        </div>

        {/* Controls Section */}
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
          {/* Year Filter */}
          <div className="flex flex-col sm:flex-row sm:items-center gap-2">
            <span className="text-sm text-muted-foreground whitespace-nowrap">Select a year to filter</span>
            <YearSelect value={year} onChange={(year) => setYear(year)} />
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-2">
            {/* Secondary Actions */}
            <div className="flex flex-col sm:flex-row gap-2">
              {!establishmentInfo?.archived ? (
                <Button
                  variant="outline"
                  onClick={() => setShowArchiveConfirmDialog(true)}
                  className="w-full sm:w-auto font-medium border-gray-300 hover:bg-gray-50"
                >
                  <Archive className="h-4 w-4 mr-2" />
                  Archive Year
                </Button>
              ) : (
                <Button
                  variant="outline"
                  onClick={() => setShowArchiveConfirmDialog(true)}
                  className="w-full sm:w-auto font-medium border-gray-300 hover:bg-gray-50"
                >
                  <Archive className="h-4 w-4 mr-2" />
                  Restore Year
                </Button>
              )}
              <Button
                variant="outline"
                onClick={handleExportPDF}
                className="w-full sm:w-auto font-medium border-gray-300 hover:bg-gray-50"
              >
                <Download className="h-4 w-4 mr-2" />
                Export PDF
              </Button>
              {/* Primary Action - Submit OSHA Report */}
              <Button onClick={() => setShowOshaSummaryConfirmDialog(true)} className="w-full sm:w-auto">
                <FileText className="h-4 w-4 mr-2" />
                Submit OSHA Report
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Content Grid */}
      <div className="space-y-4 lg:space-y-6">
        {/* Top Row - Company Info and Cases Summary */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 lg:gap-6">
          {/* Establishment Information Card */}
          <div>
            {isEditingCompanyInformation ? (
              <EstablishmentInformationForm
                year={year}
                establishmentInfo={establishmentInfo}
                onCancel={() => setIsEditingCompanyInformation(false)}
                onSave={() => setIsEditingCompanyInformation(false)}
              />
            ) : (
              <EstablishmentInformationDetails
                year={year}
                establishmentInfo={establishmentInfo}
                onEdit={() => setIsEditingCompanyInformation(true)}
              />
            )}
          </div>

          {/* Cases Summary Card */}
          <div>
            <CasesSummary summary={summary} />
          </div>

          {/* Certification Section - Full width */}
          <div>
            {isEditingCompanyExecutiveCertification ? (
              <CompanyExecutiveCertificationForm
                year={year}
                onSave={() => setIsEditingCompanyExecutiveCertification(false)}
                establishmentInfo={establishmentInfo}
              />
            ) : (
              <CompanyExecutiveCertificationDetails
                onEdit={() => setIsEditingCompanyExecutiveCertification(true)}
                establishmentInfo={establishmentInfo}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
