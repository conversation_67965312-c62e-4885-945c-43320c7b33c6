import { AsyncIncidentsSelect } from '@/components/composite/async-incidents-select';
import { EditOshaReportLoading } from '@/components/osha-reports/edit/edit-osha-report-loading';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { DateTimePicker } from '@/components/ui/date-time-picker';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { oshaTypeEnum, shiftsEnum, typeOfMedicalCareEnum } from '@shared/schema';
import {
  OSHA_TYPE_MAP,
  SHIFTS_MAP,
  TYPE_OF_MEDICAL_CARE_MAP,
  UpdateOshaReportForm,
  UpdateOshaReportFormSchema,
} from '@shared/schema.types';
import { ArrowLeft, HelpCircle, Info, Loader2, Save } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { useLocation, useParams } from 'wouter';

const FormSchema = UpdateOshaReportFormSchema;

type FormType = UpdateOshaReportForm;

export default function EditOshaReport({ params }: { params: { id: string } }) {
  const { eventId } = useParams();
  const utils = trpc.useUtils();

  // Fetch the existing CAPA data
  const oshaReportId = params.id;

  const [_, navigate] = useLocation();

  const [isSubmitting, setIsSubmitting] = useState(false);

  const { mutateAsync: updateOshaReport } = trpc.oshaReport.update.useMutation({
    onSuccess: () => {
      toast.success('OSHA Form 301 Updated', {
        description: 'The OSHA log has been updated successfully.',
      });

      utils.oshaReport.getById.invalidate({ id: oshaReportId });
    },
    onError: () => {
      toast.error('Error updating OSHA form', {
        description: 'There was a problem updating the OSHA form. Please try again.',
      });
    },
  });

  const {
    data: oshaReport,
    isLoading: isLoadingOshaReport,
    error,
  } = trpc.oshaReport.getByIdForEdit.useQuery({
    id: oshaReportId,
  });

  useEffect(() => {
    if (error?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [error]);

  const form = useForm<FormType>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      // Section 1 - Employee Info
      employeeName: '',
      employeeDepartment: '',
      employeeJobTitle: '',
      employeeWorkLocation: '',
      employeeDateOfHire: undefined,
      employeeShift: shiftsEnum.enumValues[0],
      employeeSupervisor: '',
      privacyCase: false,

      // Section 2 - Incident Details

      // Section 3 - Nature of Injury
      bodyPartInjured: '',
      typeOfInjury: '',
      treatmentLocation: '',
      typeOfMedicalCare: typeOfMedicalCareEnum.enumValues[0],
      prescribedMedication: false,

      // Section 4 - OSHA Questions
      wasHospitalized: false,
      wasDeceased: false,
      daysAwayFromWork: undefined,
      daysRestrictedFromWork: undefined,

      // Section 5 - People & Witnesses
      witnesses: '',
      reportedByName: '',

      // Section 6 - Corrective Actions
      rootCauseAnalysis: '',
      correctiveActions: '',

      // Section 7 - OSHA Reporting
      type: oshaTypeEnum.enumValues[3],
      reasonForReport: '',
      reasonForPrivacyCase: '',

      // Linked safety event
    },
    mode: 'onSubmit',
  });

  useEffect(() => {
    if (oshaReport) {
      form.reset({
        ...oshaReport,
        employeeName: oshaReport.employeeName ?? '',
        employeeDepartment: oshaReport.employeeDepartment ?? '',
        employeeJobTitle: oshaReport.employeeJobTitle ?? '',
        employeeWorkLocation: oshaReport.employeeWorkLocation ?? '',
        daysAwayFromWork: oshaReport.daysAwayFromWork ?? undefined,
        daysRestrictedFromWork: oshaReport.daysRestrictedFromWork ?? undefined,
      });
    }
  }, [oshaReport]);

  function handleBackToIncident() {
    if (eventId) {
      navigate(ROUTES.BUILD_INCIDENT_DETAILS_PATH(eventId));
    } else {
      navigate(ROUTES.BUILD_OSHA_REPORT_DETAILS_PATH(oshaReportId));
    }
  }

  async function onSubmit(values: FormType) {
    setIsSubmitting(true);

    const toUpdate = {};

    for (const field in form.formState.dirtyFields) {
      if (form.formState.dirtyFields[field as keyof typeof form.formState.dirtyFields]) {
        (toUpdate as Record<string, unknown>)[field] = values[field as keyof FormType];
      }
    }

    await updateOshaReport({
      id: oshaReportId,
      ...toUpdate,
    });

    form.reset();
    setIsSubmitting(false);
    navigate(ROUTES.BUILD_OSHA_REPORT_DETAILS_PATH(oshaReportId));
  }

  if (isLoadingOshaReport) {
    return <EditOshaReportLoading />;
  }

  return (
    <div className="container mx-auto max-w-7xl py-10 px-4 sm:px-6">
      <div className="mb-8">
        {/* Back navigation */}
        <Button variant="ghost" onClick={handleBackToIncident} className="mb-6">
          <ArrowLeft className="mr-2 h-4 w-4" />
          {eventId ? 'Back to Incident' : 'Back to OSHA Report'}
        </Button>

        {/* Header with title and action buttons */}
        <div className="flex flex-col mb-6">
          <div className="flex flex-col sm:flex-row justify-between items-start mb-2">
            <h1 className="text-2xl font-bold">OSHA Form 301: Injury and Illness Incident Report</h1>
          </div>

          {/* Status information area */}
          <div className="flex flex-wrap items-center gap-2 mt-1">
            {eventId && <span className="text-sm text-muted-foreground">Linked to Safety Event #{eventId}</span>}
          </div>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <div className="mb-6 border border-gray-200 rounded-lg p-4 bg-white">
            <div className="flex items-center mb-4">
              <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                1
              </span>
              <h2 className="text-lg font-semibold">
                Linked Safety Event <span className="text-red-500">*</span>
              </h2>
            </div>
            <p className="text-sm text-muted-foreground mb-4">This is a mandatory field.</p>
            <FormField
              control={form.control}
              name="eventId"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel className="text-base font-medium">Select Safety Event</FormLabel>
                  <AsyncIncidentsSelect
                    onChange={field.onChange}
                    value={field.value}
                    mustIncludeObjectIds={eventId ? [eventId] : undefined}
                    placeholder="Search and select a safety event..."
                  />
                  <FormDescription>
                    Associate this OSHA record with an existing safety event for better tracking.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Section 2: Employee Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  2
                </span>
                Employee Information
              </CardTitle>
              <CardDescription>Identify the employee involved in the incident</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Privacy Case Toggle */}
              <FormField
                control={form.control}
                name="privacyCase"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border border-blue-300 bg-blue-50 p-4 shadow-sm">
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className="mr-2 bg-blue-100 text-blue-800 border-blue-300 px-2 py-1">
                        🛡️ PRIVACY
                      </Badge>
                      <span className="text-md font-semibold text-blue-800">Mark as Privacy Case</span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-blue-500" />
                          </TooltipTrigger>
                          <TooltipContent className="w-[300px] p-4 bg-white shadow-lg rounded-md border">
                            <div className="space-y-2">
                              <h4 className="font-semibold text-blue-800">OSHA Privacy Case Criteria</h4>
                              <p className="text-sm text-gray-700">
                                Mark this case as private if it involves any of the following:
                              </p>
                              <ul className="text-sm text-gray-700 list-disc pl-4 space-y-1">
                                <li>Injury to intimate body part or reproductive system</li>
                                <li>Sexual assault</li>
                                <li>Mental illness</li>
                                <li>HIV infection, hepatitis, or tuberculosis</li>
                                <li>Needle stick injury from contaminated object</li>
                                <li>Employee requests privacy</li>
                              </ul>
                              <p className="text-sm text-gray-700 pt-1">
                                When enabled, the employee's name will be replaced with "Privacy Case" in OSHA records.
                              </p>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        className="data-[state=checked]:bg-blue-600"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="employeeName"
                render={({ field }) => {
                  // Check if privacy case is enabled
                  const isPrivacyCase = form.watch('privacyCase');

                  return (
                    <FormItem>
                      <FormLabel>
                        Employee Name
                        <span className="text-red-500 ml-1">*</span>
                      </FormLabel>
                      <FormControl>
                        {isPrivacyCase ? (
                          <div className="p-3 bg-blue-50 border border-blue-200 rounded-md text-blue-800 flex items-center">
                            <span className="mr-2">🛡️</span>
                            Privacy Case — name will not be displayed in OSHA logs
                          </div>
                        ) : (
                          <Input {...field} placeholder="Employee name" value={field.value ?? ''} />
                        )}
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />

              {form.watch('privacyCase') && (
                <FormField
                  control={form.control}
                  name="reasonForPrivacyCase"
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <FormLabel>
                          Reason for Privacy Case
                          <span className="text-red-500 ml-1">*</span>
                        </FormLabel>
                        <FormControl>
                          <Textarea {...field} placeholder="Reason for privacy case" value={field.value ?? ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
              )}

              <FormField
                control={form.control}
                name="employeeJobTitle"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Job Title <span className="text-red-500 ml-1">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="Job position or title" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="employeeWorkLocation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Work Location
                      <span className="text-red-500 ml-1">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Warehouse, Office, etc." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="employeeDepartment"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Department</FormLabel>
                    <FormControl>
                      <Input placeholder="Employee department" {...field} value={field.value ?? ''} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="employeeDateOfHire"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date of Hire</FormLabel>
                      <FormControl>
                        <DateTimePicker
                          selected={field.value ?? undefined}
                          onSelect={field.onChange}
                          className="w-full"
                          onlyDate
                          placeholder="Select date of hire"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="employeeShift"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Shift</FormLabel>
                      <FormControl>
                        <Select onValueChange={field.onChange} value={field.value ?? undefined}>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select shift" />
                          </SelectTrigger>
                          <SelectContent>
                            {shiftsEnum.enumValues.map((shift) => (
                              <SelectItem key={shift} value={shift}>
                                {SHIFTS_MAP[shift]}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="employeeSupervisor"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Supervisor</FormLabel>
                    <FormControl>
                      <Input placeholder="Employee's supervisor name" {...field} value={field.value ?? ''} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Section 3: Medical Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  3
                </span>
                Medical Information
              </CardTitle>
              <CardDescription>Details about the injury or illness and incident context</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="bodyPartInjured"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1">
                      Body Part Affected
                      <span className="text-red-500 ml-1">*</span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span>
                              <HelpCircle className="h-3.5 w-3.5 ml-1 text-muted-foreground" />
                            </span>
                          </TooltipTrigger>
                          <TooltipContent className="max-w-xs">
                            <p>Specify the part of body that was affected</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Hand, back, eye, etc." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="typeOfInjury"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1">
                      Type of Injury
                      <span className="text-red-500 ml-1">*</span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span>
                              <HelpCircle className="h-3.5 w-3.5 ml-1 text-muted-foreground" />
                            </span>
                          </TooltipTrigger>
                          <TooltipContent className="max-w-xs">
                            <p>Specify the type of injury sustained</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Cut, burn, fracture, etc." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="treatmentLocation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Treatment Location</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., Onsite first aid, hospital, clinic, etc."
                        {...field}
                        value={field.value ?? ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="typeOfMedicalCare"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Type of Medical Care <span className="text-red-500 ml-1">*</span>
                    </FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex flex-col space-y-1"
                      >
                        {typeOfMedicalCareEnum.enumValues.map((medicalCare) => (
                          <FormItem className="flex items-center space-x-3 space-y-0" key={medicalCare}>
                            <FormControl>
                              <RadioGroupItem value={medicalCare} />
                            </FormControl>
                            <FormLabel className="font-normal">{TYPE_OF_MEDICAL_CARE_MAP[medicalCare]}</FormLabel>
                          </FormItem>
                        ))}
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="prescribedMedication"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox checked={field.value ?? false} onCheckedChange={field.onChange} />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Prescription Medication Given</FormLabel>
                      <FormDescription>
                        Check if prescription medication was provided as part of treatment
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Section 4: OSHA Questions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  4
                </span>
                OSHA Questions
              </CardTitle>
              <CardDescription>Additional information required for OSHA Record Keeping</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="wasHospitalized"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>
                          Employee was hospitalized as an in-patient <span className="text-red-500 ml-1">*</span>
                        </FormLabel>
                        <FormDescription>Check if overnight admission to a hospital was required</FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="wasDeceased"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>
                          Incident resulted in death <span className="text-red-500 ml-1">*</span>
                        </FormLabel>
                        <FormDescription>Check if the incident resulted in a fatality</FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="daysAwayFromWork"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Days Away From Work <span className="text-red-500 ml-1">*</span>
                      </FormLabel>
                      <FormDescription>Number of days the employee was unable to work</FormDescription>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                          value={field.value ?? ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="daysRestrictedFromWork"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Days of Restricted Work Activity <span className="text-red-500 ml-1">*</span>
                      </FormLabel>
                      <FormDescription>Days employee was on restricted duty or job transfer</FormDescription>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                          value={field.value ?? ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Section 5: Witnesses & People */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  5
                </span>
                Witnesses & People
              </CardTitle>
              <CardDescription>People involved in or who witnessed the incident</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="witnesses"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Witnesses</FormLabel>
                    <FormDescription>Names of people who witnessed the incident (comma-separated list)</FormDescription>
                    <FormControl>
                      <Textarea
                        placeholder="Enter witness names separated by commas"
                        className="min-h-[60px]"
                        {...field}
                        value={field.value ?? ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="reportedByName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Reported By</FormLabel>
                    <FormDescription>
                      Optional field. Defaults to the safety event reporter. You can override this by entering a
                      different name if needed.
                    </FormDescription>
                    <FormControl>
                      <Input
                        placeholder="Enter the name of the person who reported the incident"
                        {...field}
                        value={field.value ?? ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Section 6: Corrective Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  6
                </span>
                Corrective Actions
              </CardTitle>
              <CardDescription>Actions taken or planned to prevent recurrence</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="rootCauseAnalysis"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Root Cause Analysis</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe the root cause(s) of the incident"
                        className="min-h-[80px]"
                        {...field}
                        value={field.value ?? ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="correctiveActions"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Corrective Actions</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe actions taken or planned to prevent recurrence"
                        className="min-h-[80px]"
                        {...field}
                        value={field.value ?? ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Section 7: OSHA Reporting */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  7
                </span>
                OSHA Reporting
              </CardTitle>
              <CardDescription>OSHA reporting requirements and classification</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Primary Recordable Outcome - Mandatory Dropdown */}
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      Primary Recordable Outcome
                      <span className="text-red-500 ml-1">*</span>
                    </FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select primary recordable outcome" />
                        </SelectTrigger>
                        <SelectContent>
                          {oshaTypeEnum.enumValues.map((type) => (
                            <SelectItem key={type} value={type}>
                              {OSHA_TYPE_MAP[type]}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormDescription>Select the primary outcome that makes this case OSHA recordable</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Reason for Reportability - Optional Text Area */}
              <FormField
                control={form.control}
                name="reasonForReport"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Reason for Reportability</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Optional: Provide additional explanation if the dropdown selection doesn't fully cover the situation..."
                        className="min-h-[80px] resize-none"
                        value={field.value ?? ''}
                      />
                    </FormControl>
                    <FormDescription>Optional field for additional context or explanation</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Form action buttons */}
          <div className="flex justify-end">
            {/* Right side buttons - save button only in edit mode */}
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
              Save OSHA Form
            </Button>
          </div>

          {/* OSHA Record Keeping Requirement - above submit button */}

          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mt-6">
            <div className="flex gap-3">
              <Info className="h-5 w-5 text-amber-600 flex-shrink-0 mt-0.5" />
              <div>
                <h3 className="font-medium text-amber-800">OSHA Record Keeping Requirement</h3>
                <p className="text-sm text-amber-700 mt-1">
                  This form must be retained for 5 years following the year to which it pertains. Completing this form
                  will automatically update your OSHA 300 Log and 300A Summary.
                </p>
              </div>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}
