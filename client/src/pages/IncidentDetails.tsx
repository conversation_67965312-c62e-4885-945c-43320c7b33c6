import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { CommentsSection } from '@/components/composite/comments';
import { NotifiedTeamMembers } from '@/components/composite/notified-team-members';
import { SeverityBadge } from '@/components/composite/severity-badge';
import { StatusBadge } from '@/components/composite/status-badge';
import { Timeline } from '@/components/composite/timeline';
import { IncidentDetailsError } from '@/components/incidents/details/incident-details-error';
import { IncidentDetailsLoading } from '@/components/incidents/details/incident-details-loading';
import { Insight } from '@/components/incidents/details/insight';
import { LinkedCapas } from '@/components/incidents/details/linked-capas';
import { OshaDetails } from '@/components/incidents/details/osha-details';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '@/components/ui/carousel';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MediaViewerModal } from '@/components/ui/media-viewer-modal';
import { useAnalytics } from '@/hooks/use-analytics';
import { usePermissions } from '@/hooks/use-permissions';
import { trpc } from '@/providers/trpc';
import { formatDate } from '@shared/date-utils';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { REPORT_TYPE_MAP } from '@shared/schema.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { Archive, Calendar, ChevronLeft, Edit, Mail, MapPin, MoreVertical, Play, User } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'wouter';

// Main component
export default function IncidentDetails({ params }: { params: { id: string } }) {
  const eventId = params.id;
  const [_, navigate] = useLocation();
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [isViewerOpen, setIsViewerOpen] = useState(false);
  const [viewerIndex, setViewerIndex] = useState(0);
  const { hasPermission } = usePermissions();
  const { track } = useAnalytics();

  const {
    data: event,
    isLoading,
    error,
    isSuccess,
  } = trpc.incident.getById.useQuery({
    id: eventId,
  });

  useEffect(() => {
    if (error?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [error]);

  // Track incident details view
  useEffect(() => {
    if (event && isSuccess) {
      track(ANALYTICS_EVENTS.EVENT.DETAIL_VIEW_OPENED, {
        event_id: event.id,
        current_status: event.status,
        severity: event.severity,
        report_type: REPORT_TYPE_MAP[event.type],
        access_scope: 'global',
        is_ai_summary_present: false,
      });
    }
  }, [event, isSuccess]);

  // Format the datetime
  const formattedDate = useMemo(() => formatDate(event?.reportedAt), [event?.reportedAt]);

  // Determine if OSHA reportable
  const isOshaReportable = useMemo(() => event?.oshaReportable, [event]);

  // Helper function to track edit initiated
  const handleEditClick = () => {
    track(ANALYTICS_EVENTS.EVENT.EDIT_INITIATED, {
      event_id: eventId,
      source: 'detail_page',
    });
    navigate(ROUTES.BUILD_INCIDENT_EDIT_PATH(eventId));
  };

  // Handle media thumbnail click to open viewer
  const handleMediaClick = (index: number) => {
    setViewerIndex(index);
    setIsViewerOpen(true);
  };

  if (isLoading) {
    return <IncidentDetailsLoading />;
  }

  if (error || !event) {
    return <IncidentDetailsError />;
  }

  return (
    <div className="container mx-auto py-4 px-4">
      {/* Back button */}
      <div className="mb-3">
        <Button variant="ghost" onClick={() => navigate(ROUTES.INCIDENT_LIST)}>
          <ChevronLeft className="h-4 w-4 mr-1" />
          <span>Back</span>
        </Button>
      </div>

      <>
        {/* Incident Header */}
        <div className="flex flex-col md:flex-row justify-between items-start gap-3 mb-3">
          {/* Desktop View */}
          <div className="hidden md:block w-full">
            <div className="flex flex-wrap items-center gap-2 mb-1">
              <span className="text-sm font-medium text-muted-foreground">{event.slug}</span>
              <StatusBadge status={event.status} />
              <SeverityBadge severity={event.severity} />
              {isOshaReportable && (
                <Badge className="bg-red-50 text-red-600 border border-red-200" variant="outline">
                  OSHA Reportable
                </Badge>
              )}
              <Badge className="capitalize bg-slate-50 text-slate-700 border border-slate-200" variant="outline">
                {REPORT_TYPE_MAP[event.type]}
              </Badge>
              {event.archived && (
                <Badge className="bg-amber-50 text-amber-600 border-amber-200 font-medium" variant="outline">
                  <Archive className="h-3 w-3 mr-1" />
                  Archived
                </Badge>
              )}
            </div>
            <h1 className="text-2xl md:text-3xl font-bold mb-2">{event.title}</h1>
          </div>

          {/* Mobile View - with menu in title area */}
          <div className="md:hidden w-full">
            <div className="flex flex-wrap items-center gap-2 mb-1">
              <span className="text-sm font-medium text-muted-foreground">{event.slug}</span>
              <StatusBadge status={event.status || 'Open'} />
              <SeverityBadge severity={event.severity} />
              {isOshaReportable && (
                <Badge className="bg-red-50 text-red-600 border border-red-200" variant="outline">
                  OSHA Reportable
                </Badge>
              )}
              <Badge className="capitalize bg-slate-50 text-slate-700 border border-slate-200" variant="outline">
                {REPORT_TYPE_MAP[event.type]}
              </Badge>
              {event.archived && (
                <Badge className="bg-amber-50 text-amber-600 border-amber-200 font-medium" variant="outline">
                  <Archive className="h-3 w-3 mr-1" />
                  Archived
                </Badge>
              )}
            </div>

            {/* Mobile title row with menu */}
            <div className="flex items-center justify-between">
              <h1 className="text-2xl font-bold mb-2 pr-3 flex-1">{event.title}</h1>

              {/* Mobile menu in the title row */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-9 w-9 p-0 flex items-center justify-center"
                    aria-label="Open actions menu"
                  >
                    <MoreVertical className="h-5 w-5" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>Safety Event Actions</DropdownMenuLabel>
                  <DropdownMenuSeparator />

                  {hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.EDIT, event.reportedByUser?.id) && (
                    <DropdownMenuItem className="h-11 cursor-pointer" onClick={handleEditClick}>
                      <Edit className="mr-2 h-4 w-4" />
                      <span>Edit Safety Event</span>
                    </DropdownMenuItem>
                  )}

                  <DropdownMenuSeparator />

                  {hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.EDIT, event.reportedByUser?.id) && (
                    <DropdownMenuItem className="h-11 cursor-pointer" onClick={() => setShowArchiveConfirm(true)}>
                      <Archive className="mr-2 h-4 w-4" />
                      <span>{event.archived ? 'Unarchive Safety Event' : 'Archive Safety Event'}</span>
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Desktop buttons - only shown on desktop */}
          <div className="hidden md:flex gap-2 self-start">
            {hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.EDIT, event.reportedByUser?.id) && (
              <Button size="sm" onClick={handleEditClick}>
                <Edit className="h-4 w-4" />
                <span>Edit</span>
              </Button>
            )}

            {/* Desktop-only Options Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-1">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Options</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.EDIT, event.reportedByUser?.id) && (
                  <DropdownMenuItem onClick={() => setShowArchiveConfirm(true)}>
                    <Archive className="mr-2 h-4 w-4" />
                    <span>{event.archived ? 'Unarchive Safety Event' : 'Archive Safety Event'}</span>
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Context bar with metadata */}
        <div className="flex flex-wrap items-center text-sm text-muted-foreground mb-4 gap-y-2">
          <div className="flex items-center">
            <Calendar className="h-4 w-4 mr-1.5" />
            <span>{formattedDate}</span>
          </div>

          <div className="hidden sm:block text-muted-foreground mx-2">•</div>

          <div className="flex items-center">
            <User className="h-4 w-4 mr-1.5" />
            <span>{event.reportedByUser?.fullName ?? event.reportedByName}</span>
          </div>
          <div className="hidden sm:block text-muted-foreground mx-2">•</div>

          <div className="flex items-center">
            <Mail className="h-4 w-4 mr-1.5" />
            <span>{event.reportedByUser?.email ?? event.reportedByEmail}</span>
          </div>
        </div>

        {/* Main content grid */}
        <div
          className={`grid grid-cols-1 md:grid-cols-3 gap-4 ${
            event.archived ? 'p-3 bg-amber-50/30 border border-amber-200 rounded-lg' : ''
          }`}
        >
          {/* Left column - main content */}
          <div className="md:col-span-2 space-y-4">
            {/* AI Safety Insight - Enhanced */}
            <Insight incident={event} />

            {/* OSHA Submission Banner - Show green success banner if submitted, yellow alert if not */}
            {isOshaReportable && <OshaDetails eventId={eventId} />}

            {/* Description */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Description</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-gray-700 whitespace-pre-wrap leading-relaxed">
                  {event.description || 'No description provided.'}
                </div>
              </CardContent>
            </Card>

            {/* Media */}
            {event.media && event.media.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Media</CardTitle>
                </CardHeader>
                <CardContent>
                  <Carousel className="w-full">
                    <CarouselContent>
                      {event.media.map((file, index) => (
                        <CarouselItem key={index} className="basis-full sm:basis-1/2 md:basis-1/3">
                          <div
                            className="overflow-hidden rounded-md bg-muted h-40 flex items-center justify-center relative cursor-pointer hover:opacity-90 transition-opacity"
                            onClick={() => handleMediaClick(index)}
                          >
                            {file.type?.startsWith('image/') ? (
                              <img src={file.url} alt={file.name} className="h-full w-full object-cover" />
                            ) : file.type?.startsWith('video/') ? (
                              <div className="relative h-full w-full">
                                <video src={file.url} className="h-full w-full object-cover" preload="metadata" muted>
                                  Your browser does not support the video tag.
                                </video>
                                {/* Play icon overlay */}
                                <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                                  <div className="flex items-center justify-center w-10 h-10 bg-white/90 rounded-full shadow-lg">
                                    <Play className="h-5 w-5 text-gray-700 ml-0.5" fill="currentColor" />
                                  </div>
                                </div>
                              </div>
                            ) : (
                              <img src={file.url} alt={file.name} className="h-full w-full object-cover" />
                            )}
                          </div>
                        </CarouselItem>
                      ))}
                    </CarouselContent>
                    <CarouselPrevious className="left-2" />
                    <CarouselNext className="right-2" />
                  </Carousel>
                </CardContent>
              </Card>
            )}

            {/* Immediate Actions */}
            {event.immediateActions && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Immediate Actions Taken</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-gray-700 whitespace-pre-wrap leading-relaxed">{event.immediateActions}</div>
                </CardContent>
              </Card>
            )}

            <LinkedCapas eventId={eventId} />

            {/* Comments Section - Moved to match CAPA view layout */}
            {/* TODO: Work with context to get the entity details */}
            <CommentsSection
              entityId={event.id}
              entityType="event"
              entitySlug={event.slug!}
              entityTitle={event.title}
              status={event.status}
            />
          </div>

          {/* Right column - metadata & timeline */}
          <div className="space-y-4">
            {/* Location and Linked Assets */}
            {(event.location || (event.assets && event.assets.length > 0)) && (
              <Card>
                <CardHeader>
                  <CardTitle>Location and Linked Assets</CardTitle>
                </CardHeader>
                <CardContent>
                  {event.location && (
                    <div className="flex flex-col gap-2">
                      <div className="flex items-center gap-2 mb-4">
                        <MapPin className="h-4 w-4 text-gray-500" />
                        <span>{event.location?.name}</span>
                      </div>
                    </div>
                  )}
                  {Array.isArray(event.assets) && event.assets.length > 0 && (
                    <div className="flex flex-col gap-2">
                      <div className="flex flex-wrap items-center gap-2">
                        {event.assets.map((asset) => (
                          <Badge variant="outline" key={asset.id}>
                            {asset.name}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
            {event.teamMembersToNotify && event.teamMembersToNotify.length > 0 && (
              <NotifiedTeamMembers users={event.teamMembersToNotify} />
            )}
            <Timeline entityId={eventId} entityType="event" />
          </div>
        </div>
      </>
      {/* Archive Confirmation Dialog */}
      <ArchiveConfirmationDialog
        archived={event?.archived || false}
        showDialog={showArchiveConfirm}
        setShowDialog={setShowArchiveConfirm}
        entityId={event.id}
        entityType="event"
      />

      {/* Media Viewer Modal */}
      {event.media && event.media.length > 0 && (
        <MediaViewerModal
          files={event.media}
          initialIndex={viewerIndex}
          isOpen={isViewerOpen}
          onClose={() => setIsViewerOpen(false)}
        />
      )}
    </div>
  );
}
