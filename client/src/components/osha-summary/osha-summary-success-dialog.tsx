import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogAction,
} from '@/components/ui/alert-dialog';

export const OshaSummarySuccessDialog = ({
  showDialog,
  setShowDialog,
  year,
}: {
  showDialog: boolean;
  setShowDialog: (show: boolean) => void;
  year: number;
}) => {
  return (
    <AlertDialog open={showDialog} onOpenChange={setShowDialog}>
      <AlertDialogContent className="max-w-2xl">
        <AlertDialogHeader>
          <AlertDialogTitle className="text-xl font-semibold text-green-700">
            OSHA 300A Data Prepared for E-Submission
          </AlertDialogTitle>
          <AlertDialogDescription className="text-base">
            <div className="space-y-4">
              <div className="font-medium">
                Your OSHA Form 300A data has been successfully prepared for electronic submission.
              </div>

              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <div className="font-semibold text-blue-900 mb-2">Key Instruction:</div>
                <div className="text-blue-800">
                  Please upload the downloaded file (osha_300a_summary_{year}.csv) to OSHA's official Injury Tracking
                  Application (ITA) website by March 2nd.
                </div>
              </div>

              <div className="space-y-2">
                <div className="font-medium">Access the ITA Portal:</div>
                <a
                  href="https://www.osha.gov/injuryreporting"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-blue-600 hover:text-blue-800 underline"
                >
                  https://www.osha.gov/injuryreporting
                  <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                    />
                  </svg>
                </a>
              </div>

              <div className="bg-gray-50 p-3 rounded border-l-4 border-gray-400">
                <div className="text-sm text-gray-700">
                  <strong>Record Retention:</strong> Remember to keep all your OSHA records (Forms 300, 300A, and 301)
                  for 5 years following the year to which they pertain.
                </div>
              </div>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogAction onClick={() => setShowDialog(false)}>Got It</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
