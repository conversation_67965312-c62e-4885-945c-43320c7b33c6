import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { FileText } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

export const OshaSummaryConfirmationDialog = ({
  showDialog,
  setShowDialog,
  year,
  onGenerateAndDownloadReport,
}: {
  showDialog: boolean;
  setShowDialog: (show: boolean) => void;
  year: number;
  onGenerateAndDownloadReport: () => void;
}) => {
  const [certificationAgreed, setCertificationAgreed] = useState(false);

  const handleGenerateAndDownloadReport = () => {
    if (!certificationAgreed) {
      toast.error('Certification Required', {
        description: 'You must affirm the certification statements before proceeding.',
      });
      return;
    }

    onGenerateAndDownloadReport();
  };

  return (
    <Dialog open={showDialog} onOpenChange={setShowDialog}>
      <DialogContent className="max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Important: Confirm OSHA Report Generation & Submission</DialogTitle>
          <DialogDescription asChild>
            <div className="space-y-6">
              {/* Certification & Responsibility Section */}
              <div className="bg-blue-50 border-l-4 border-blue-500 p-6 rounded-r-lg">
                <div className="font-bold text-blue-800 text-lg mb-3">📋 Your Responsibility & Certification</div>
                <div className="text-blue-900 space-y-3">
                  <p className="font-semibold mb-4">Please affirm the following statements before proceeding:</p>
                  <ul className="list-disc pl-5 space-y-3">
                    <li>
                      <strong>
                        I certify that the information I have entered into this system is accurate and complete to the
                        best of my knowledge.
                      </strong>{' '}
                      (This links to the executive certification on the 300A)
                    </li>
                    <li>
                      <strong>
                        I understand that UpKeep provides this data in an applicable format for my convenience, but I
                        remain solely responsible for the accuracy and completeness of the data, and to review that the
                        form is formatted correctly, prior to submission.
                      </strong>
                    </li>
                    <li>
                      <strong>
                        I understand that UpKeep is not responsible for the actual electronic submission to OSHA's
                        Injury Tracking Application (ITA). I am solely responsible for uploading the generated file and
                        ensuring full compliance with all applicable OSHA regulations and deadlines.
                      </strong>
                    </li>
                    <li>
                      <strong>I have reviewed all data carefully, and agree to these terms before proceeding.</strong>
                    </li>
                  </ul>
                </div>
              </div>

              {/* Mandatory Agreement Checkbox */}
              <div className="bg-amber-50 border border-amber-200 p-4 rounded-lg">
                <div className="flex items-start space-x-3">
                  <Checkbox
                    id="certification-agreement"
                    checked={certificationAgreed}
                    onCheckedChange={(checked) => setCertificationAgreed(checked === true)}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <label
                      htmlFor="certification-agreement"
                      className="text-sm font-medium text-amber-900 cursor-pointer leading-relaxed"
                    >
                      I accept and agree with the statements above.
                    </label>
                  </div>
                </div>
              </div>

              {/* Action Confirmation Section */}
              <div className="bg-blue-50 border border-blue-200 p-6 rounded-lg">
                <div className="font-semibold text-blue-900 text-lg mb-3">What Happens Next</div>
                <div className="text-blue-800 space-y-3">
                  <p>
                    Upon clicking <strong>'Generate & Download Report'</strong>, a CSV file containing your OSHA Form
                    300A data will be generated and downloaded to your device.
                  </p>
                  <p>
                    <strong>File Name:</strong> osha_300a_summary_{year}.csv
                  </p>
                  <p>
                    <strong>Next Step:</strong> You must upload this file to OSHA's official Injury Tracking Application
                    (ITA) website by March 2nd for your applicable reporting year.
                  </p>
                </div>
              </div>

              {/* Important Reminders */}
              <div className="bg-gray-50 border border-gray-200 p-6 rounded-lg">
                <div className="font-semibold text-gray-800 text-lg mb-3">Important Reminders</div>
                <div className="text-gray-700 space-y-2">
                  <p>• Review all data carefully before proceeding with submission</p>
                  <p>• Ensure your company executive has certified the information</p>
                  <p>• Keep records of your submission for compliance purposes</p>
                  <p>• The submission deadline is annually, March 2nd</p>
                </div>
              </div>
            </div>
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="gap-4 pt-6">
          <Button
            variant="outline"
            onClick={() => {
              setShowDialog(false);
              setCertificationAgreed(false); // Reset checkbox when modal is closed
            }}
          >
            Cancel
          </Button>
          <Button onClick={handleGenerateAndDownloadReport} disabled={!certificationAgreed}>
            <FileText className="h-4 w-4 mr-2" />
            Generate & Download Report
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
