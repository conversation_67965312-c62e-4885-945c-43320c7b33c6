import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { SeverityBadge } from '@/components/composite/severity-badge';
import { StatusBadge } from '@/components/composite/status-badge';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { usePermissions } from '@/hooks/use-permissions';
import { useAnalytics } from '@/hooks/use-analytics';
import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { RouterOutputs } from '@shared/router.types';
import { REPORT_TYPE_MAP } from '@shared/schema.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { format } from 'date-fns';
import { Archive, Calendar, Eye, Info, MapPin, MoreHorizontal, Pencil, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { useLocation } from 'wouter';

export const IncidentsMobileView = ({
  incidents,
  activeFilterCount,
  resetFilters,
}: {
  incidents: RouterOutputs['incident']['list']['result'];
  activeFilterCount: number;
  resetFilters: () => void;
}) => {
  const [_, navigate] = useLocation();
  const analytics = useAnalytics();
  const { hasPermission } = usePermissions();
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [selectedIncident, setSelectedIncident] = useState<RouterOutputs['incident']['list']['result'][number] | null>(
    null,
  );
  const handleRowAction = (incidentId: string, action: 'View' | 'Edit') => {
    analytics.track(ANALYTICS_EVENTS.EVENT.ROW_ACTION_CLICKED, {
      event_id: incidentId,
      action,
    });
  };

  return (
    <div className="space-y-4">
      {selectedIncident && (
        <ArchiveConfirmationDialog
          archived={selectedIncident?.archived || false}
          showDialog={showArchiveConfirm}
          setShowDialog={setShowArchiveConfirm}
          entityId={selectedIncident.id}
          entityType="event"
        />
      )}
      {incidents.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-8">
          <Info className="h-12 w-12 text-muted-foreground mb-3" />
          <p className="text-muted-foreground text-center mb-2">No safety events found</p>
          {activeFilterCount > 0 && (
            <Button variant="link" onClick={resetFilters}>
              Clear filters
            </Button>
          )}
        </div>
      ) : (
        incidents.map((incident) => (
          <Card
            key={incident.id}
            className={`mb-4 cursor-pointer transition-colors hover:bg-muted/50 ${
              incident.archived ? 'bg-amber-50/50 hover:bg-amber-50/80 border-amber-200' : ''
            }`}
            onClick={() => {
              handleRowAction(incident.id, 'View');
              navigate(ROUTES.BUILD_INCIDENT_DETAILS_PATH(incident.id));
            }}
          >
            <CardContent className="p-4">
              <div className="flex justify-between items-start mb-2">
                <div className="flex items-center">
                  <p className="font-medium text-sm mr-2">{incident.slug}</p>
                  {incident.oshaReportable && (
                    <Badge className="bg-red-50 text-red-600 border-red-200 mr-1" variant="outline">
                      OSHA
                    </Badge>
                  )}
                  {incident.archived && (
                    <Badge className="bg-amber-50 text-amber-600 border-amber-200" variant="outline">
                      <Archive className="h-3 w-3 mr-1" />
                      Archived
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-1">
                  <StatusBadge status={incident.status} />
                  <SeverityBadge severity={incident.severity} />
                </div>
              </div>

              <h3 className="font-medium mb-2 line-clamp-2">{incident.title}</h3>

              <div className="text-sm text-muted-foreground mb-3">
                <div className="flex items-center mb-1">
                  <Badge className="bg-slate-50 text-slate-700 mr-2 capitalize whitespace-nowrap" variant="outline">
                    {REPORT_TYPE_MAP[incident.type as keyof typeof REPORT_TYPE_MAP]}
                  </Badge>
                  <span className="flex items-center">
                    <Calendar className="h-3 w-3 mr-1" />
                    {format(new Date(incident.reportedAt), 'MMM d, yyyy')}
                  </span>
                </div>

                <div className="flex items-center mb-1">
                  <MapPin className="h-3 w-3 mr-1" />
                  {incident?.location?.name || 'No location'}
                </div>
              </div>

              <div className="flex justify-between items-center">
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    className="h-8 px-2 rounded-full"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRowAction(incident.id, 'View');
                      navigate(ROUTES.BUILD_INCIDENT_DETAILS_PATH(incident.id));
                    }}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    className="h-8 px-2 rounded-full"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRowAction(incident.id, 'Edit');
                      navigate(ROUTES.BUILD_INCIDENT_EDIT_PATH(incident.id));
                    }}
                  >
                    <Pencil className="h-4 w-4" />
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-8 px-2 rounded-full"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" onClick={(e) => e.stopPropagation()}>
                      {hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.EDIT) && (
                        <DropdownMenuItem
                          className={incident.archived ? 'text-amber-600' : 'text-red-600'}
                          onClick={async (e) => {
                            e.stopPropagation();
                            setSelectedIncident(incident);
                            setShowArchiveConfirm(true);
                            toast.success(incident.archived ? 'Incident Unarchived' : 'Incident Archived', {
                              description: incident.archived
                                ? `${incident.slug} has been restored to its previous status.`
                                : `${incident.slug} has been archived.`,
                            });
                          }}
                        >
                          {incident.archived ? (
                            <>
                              <Archive className="h-4 w-4 mr-2" />
                              Unarchive
                            </>
                          ) : (
                            <>
                              <Trash2 className="h-4 w-4 mr-2" />
                              Archive
                            </>
                          )}
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </CardContent>
          </Card>
        ))
      )}
    </div>
  );
};
