import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useAnalytics } from '@/hooks/use-analytics';
import { ChevronDown, Download, Upload } from 'lucide-react';
import { toast } from 'sonner';

interface AccessPointBulkActionsDropdownProps {
  onImportClick: () => void;
}

export const AccessPointBulkActionsDropdown = ({ onImportClick }: AccessPointBulkActionsDropdownProps) => {
  const { track } = useAnalytics();

  // Handle template download
  const handleDownloadTemplate = () => {
    const toastWrapper = (msg: string, options?: unknown) => {
      const description =
        options && typeof options === 'object' && 'description' in options
          ? String((options as { description: unknown }).description)
          : undefined;

      toast.info(msg, description ? { description } : undefined);
    };

    const url = `${window.location.origin}/ehs/access-point-sample.csv`;
    const filename = 'access-point-template.csv';

    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toastWrapper('Download started');
  };

  // Handle bulk import initiation
  const handleImportClick = () => {
    track(ANALYTICS_EVENTS.ACCESS_POINT.BULK_IMPORT_INITIATED, {});
    onImportClick();
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline">
          <Upload className="h-4 w-4 mr-2" />
          Bulk Actions
          <ChevronDown className="h-4 w-4 ml-2" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={handleDownloadTemplate}>
          <Download className="h-4 w-4 mr-2" />
          Download Template
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleImportClick}>
          <Upload className="h-4 w-4 mr-2" />
          Import Access Points
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
