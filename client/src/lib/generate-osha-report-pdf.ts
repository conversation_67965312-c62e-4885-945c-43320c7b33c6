import { format } from 'date-fns';
import { RouterOutputs } from '@shared/router.types';
import { formatDate } from '@shared/date-utils';
import {
  OSHA_TYPE_MAP,
  REPORT_TYPE_MAP,
  SEVERITY_MAP,
  SHIFTS_MAP,
  STATUS_MAP,
  TYPE_OF_MEDICAL_CARE_MAP,
} from '@shared/schema.types';

type OshaReport = RouterOutputs['oshaReport']['getById'];

// Main export function
export const generateOshaReportPdf = (data: OshaReport): void => {
  // Create a printable div for the OSHA Form 301 - professional layout
  const printDiv = document.createElement('div');
  printDiv.style.width = '8.5in';
  printDiv.style.minHeight = '11in';
  printDiv.style.margin = '0 auto';
  printDiv.style.padding = '0.5in';
  printDiv.style.fontFamily = '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif';
  printDiv.style.fontSize = '10pt';
  printDiv.style.position = 'relative';
  printDiv.style.lineHeight = '1.3';
  printDiv.style.backgroundColor = '#ffffff';
  printDiv.style.color = '#1a1a1a';
  printDiv.style.boxSizing = 'border-box';

  // OSHA Header Section - with official logo
  const headerSection = document.createElement('div');
  headerSection.style.textAlign = 'center';
  headerSection.style.marginBottom = '0.2in';
  headerSection.style.borderBottom = '2px solid #0066cc';
  headerSection.style.paddingBottom = '0.1in';
  headerSection.innerHTML = `
    <table style="width: 100%; margin-bottom: 0.1in;">
      <tr>
        <td style="width: 120px; text-align: center; vertical-align: middle;">
          <div style="width: 100px; height: 60px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
            <svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" viewBox="0 0 720.002 207.213" style="width: 100px; height: auto;">
              <path d="M314.039 48.588c-1.656-13.246-2.76-26.492-2.76-39.465-8.557-1.102-32.014-4.139-39.738-4.139-35.602 0-60.988 21.25-60.988 57.127 0 66.51 81.131 51.332 81.131 100.18 0 19.594-15.178 31.461-33.943 31.461-26.217 0-39.463-20.422-42.773-43.879l-7.729 1.654c1.381 14.074 3.314 27.875 4.691 41.949 13.523 6.9 28.426 12.42 43.881 12.42 35.322 0 66.783-19.873 66.783-57.955 0-66.236-82.789-52.436-82.789-98.248 0-21.805 12.143-34.773 33.941-34.773 19.043 0 30.635 17.111 32.564 35.049"/>
              <path d="M372.752 109.027h100.176v68.719c0 14.904-11.592 15.73-21.523 15.73h-5.246v8.279c12.143 0 29.805-1.104 44.707-1.104 14.074 0 30.633 1.104 41.67 1.104v-8.279h-3.861c-9.936 0-20.422-1.381-20.422-15.73V33.133c0-14.348 10.486-15.729 20.422-15.729h3.861V9.123c-11.037 0-27.596 1.105-41.67 1.105-14.902 0-32.564-1.105-44.707-1.105v8.281h5.246c9.932 0 21.523.826 21.523 15.729v60.439H372.752V33.133c0-14.902 11.592-15.729 21.525-15.729h5.244V9.123c-12.143 0-29.805 1.105-44.709 1.105-14.072 0-30.629-1.105-41.668-1.105v8.281h3.861c9.936 0 20.422 1.381 20.422 15.729v144.613c0 14.35-10.486 15.73-20.422 15.73h-3.861v8.279c11.039 0 27.596-1.104 41.668-1.104 14.904 0 32.566 1.104 44.709 1.104v-8.279h-5.244c-9.934 0-21.525-.826-21.525-15.73"/>
              <path d="M581.742 133.59h62.645l10.762 32.012c3.037 8.832 5.52 16.283 5.52 19.873 0 6.898-10.762 8.002-17.109 8.002h-3.035v8.279c14.076-.551 27.875-1.104 41.395-1.104 13.246 0 25.664.553 38.084 1.104v-8.279h-1.656c-9.105 0-14.9-3.311-18.213-11.039-3.588-8.279-6.898-18.49-10.211-28.15L639.145 9.951c-.83-2.207-1.656-4.691-2.484-6.898-.551-.83-1.104-.83-1.932-.83s-1.379.279-2.207.553c-5.244 3.312-16.283 8.832-25.113 11.867-1.654 10.213-6.623 24.012-10.211 34.223L553.042 175.54c-4.139 11.59-13.244 17.938-24.008 17.938h-1.654v8.279c9.934-.551 19.867-1.104 29.805-1.104 11.035 0 22.352.553 33.391 1.104v-8.279h-3.035c-9.109 0-20.699-1.381-20.699-9.66 0-4.967 3.312-12.143 6.07-21.525m65.68-44.158h-51.605l25.664-78.102h.555l25.386 78.102z"/>
              <path fill="#939598" d="M56.324 71.451c10.143-14.01 26.633-23.123 45.25-23.123 30.836 0 55.832 24.996 55.832 55.834 0 30.836-24.996 55.832-55.832 55.832-22.824 0-42.449-13.695-51.105-33.32 6.953 7.992 17.197 13.043 28.623 13.043 20.947 0 37.928-16.982 37.928-37.93s-16.98-37.928-37.928-37.928a37.774 37.774 0 0 0-22.768 7.592"/>
              <path fill="#0082C4" d="M0 103.941C0 60.33 35.352 24.975 78.963 24.975c28.4 0 53.299 14.996 67.213 37.502-11.141-11.916-27.002-19.363-44.602-19.363-33.713 0-61.045 27.332-61.045 61.049s27.332 61.049 61.045 61.049c17.168 0 32.68-7.086 43.771-18.494-14.057 21.775-38.537 36.189-66.383 36.189C35.352 182.906 0 147.553 0 103.941"/>
              <path d="M5.836 61.889C21.92 25.688 58.189.443 100.357.443c57.094 0 103.379 46.285 103.379 103.383 0 57.1-46.285 103.387-103.379 103.387-41.775 0-77.764-24.781-94.068-60.441 14.674 24.846 41.727 41.512 72.674 41.512 46.58 0 84.34-37.762 84.34-84.342 0-46.584-37.76-84.344-84.34-84.344-31.268 0-58.563 17.013-73.127 42.291M664.98 19.18C664.98 8.592 673.572 0 684.262 0c10.639 0 19.18 8.592 19.18 19.18 0 10.689-8.541 19.281-19.18 19.281A19.23 19.23 0 0 1 664.98 19.18m35.084 0c0-8.695-7.059-15.805-15.803-15.805-8.797 0-15.908 7.109-15.908 15.805 0 8.797 7.111 15.906 15.908 15.906 8.745 0 15.803-7.109 15.803-15.906zm-19.894 1.277v9.771h-3.529V7.621h7.314c4.141 0 8.438 1.123 8.438 6.24 0 2.607-1.584 4.652-4.604 5.266v.104c3.121.613 3.479 1.994 3.838 4.449.307 2.146.562 4.502 1.328 6.549h-4.502c-.254-1.279-.613-2.713-.768-4.041-.252-1.945-.252-3.736-1.277-4.809-.867-.922-2.045-.818-3.273-.922h-2.965zm3.73-3.527c3.326-.104 4.092-1.484 4.092-3.223 0-1.689-.766-2.557-3.578-2.557h-4.244v5.779h3.73z"/>
            </svg>
          </div>
        </td>
        <td style="text-align: left; vertical-align: middle; padding-left: 0.2in;">
          <h1 style="font-size: 16pt; margin: 0; color: #0066cc; font-weight: 700;">Form 301</h1>
          <h2 style="font-size: 11pt; margin: 0; color: #333; font-weight: 500;">Injury and Illness Incident Report</h2>
        </td>
      </tr>
    </table>
    <div style="background: #f8f9fa; padding: 0.08in; border-radius: 4px; border: 1px solid #dee2e6;">
      <p style="margin: 0; font-size: 8pt; color: #666; font-style: italic;">
        This form is required by OSHA regulations to record details about workplace injuries and illnesses.
      </p>
      <p style="margin: 0.05in 0 0 0; font-size: 12pt; color: #0066cc; font-weight: 600;">Report ID: ${data.slug || 'N/A'}</p>
    </div>
  `;
  printDiv.appendChild(headerSection);

  // Employee Information Section
  const employeeSection = document.createElement('div');
  employeeSection.style.marginBottom = '0.15in';
  employeeSection.style.background = '#f8f9fa';
  employeeSection.style.border = '1px solid #dee2e6';
  employeeSection.style.borderRadius = '4px';
  employeeSection.style.padding = '0.1in';
  employeeSection.innerHTML = `
    <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #0066cc; font-weight: 600; border-bottom: 1px solid #0066cc; padding-bottom: 0.03in;">
      Employee Information
    </h3>
    <table style="width: 100%; font-size: 9pt; border-collapse: collapse;">
      <tr>
        <td style="width: 50%; vertical-align: top; padding-right: 0.1in;">
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #0066cc; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Employee Name:</strong><br>
            <span style="color: #212529; font-size: 10pt; font-weight: 600;">
              ${data.privacyCase ? 'Privacy Case' : data.employeeName || 'Not specified'}
            </span>
            ${data.privacyCase ? `<br><span style="color: #dc3545; font-size: 8pt; font-style: italic;">Reason: ${data.reasonForPrivacyCase || 'Not specified'}</span>` : ''}
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #0066cc; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Job Title:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.employeeJobTitle || 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #0066cc; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Department:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.employeeDepartment || 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #0066cc;">
            <strong style="color: #495057;">Work Location:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.employeeWorkLocation || 'Not specified'}</span>
          </div>
        </td>
        <td style="width: 50%; vertical-align: top; padding-left: 0.1in;">
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #28a745; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Date of Hire:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.employeeDateOfHire ? formatDate(data.employeeDateOfHire) : 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #28a745; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Shift:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.employeeShift ? SHIFTS_MAP[data.employeeShift] : 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #28a745;">
            <strong style="color: #495057;">Supervisor:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.employeeSupervisor || 'Not specified'}</span>
          </div>
        </td>
      </tr>
    </table>
  `;
  printDiv.appendChild(employeeSection);

  // Incident Details Section
  const incidentSection = document.createElement('div');
  incidentSection.style.marginBottom = '0.15in';
  incidentSection.style.background = '#f8f9fa';
  incidentSection.style.border = '1px solid #dee2e6';
  incidentSection.style.borderRadius = '4px';
  incidentSection.style.padding = '0.1in';
  incidentSection.innerHTML = `
    <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #0066cc; font-weight: 600; border-bottom: 1px solid #0066cc; padding-bottom: 0.03in;">
      Incident Details
    </h3>
    <table style="width: 100%; font-size: 9pt; border-collapse: collapse;">
      <tr>
        <td style="width: 50%; vertical-align: top; padding-right: 0.1in;">
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #fd7e14; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Incident Title:</strong><br>
            <span style="color: #212529; font-size: 10pt; font-weight: 600;">${data.event?.title || 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #fd7e14; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Date Reported:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.event?.reportedAt ? formatDate(data.event.reportedAt) : 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #fd7e14; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Reported By:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.reportedByName || data.event?.reportedBy?.fullName || data.event?.reportedByName || 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #fd7e14;">
            <strong style="color: #495057;">Location:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.event?.location?.name || 'Not specified'}</span>
          </div>
        </td>
        <td style="width: 50%; vertical-align: top; padding-left: 0.1in;">
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #6f42c1; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Incident Type:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.event?.type ? REPORT_TYPE_MAP[data.event.type] : 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #6f42c1; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Severity:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.event?.severity ? SEVERITY_MAP[data.event.severity] : 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #6f42c1;">
            <strong style="color: #495057;">Status:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.event?.status ? STATUS_MAP[data.event?.status] : 'Not specified'}</span>
          </div>
        </td>
      </tr>
    </table>
    ${
      data.event?.description
        ? `
      <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #17a2b8; margin-top: 0.05in;">
        <strong style="color: #495057;">Description:</strong><br>
        <span style="color: #212529; font-size: 9pt; line-height: 1.4;">${data.event.description}</span>
      </div>
    `
        : ''
    }
  `;
  printDiv.appendChild(incidentSection);

  // Injury Details Section
  const injurySection = document.createElement('div');
  injurySection.style.marginBottom = '0.15in';
  injurySection.style.background = '#f8f9fa';
  injurySection.style.border = '1px solid #dee2e6';
  injurySection.style.borderRadius = '4px';
  injurySection.style.padding = '0.1in';
  injurySection.innerHTML = `
    <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #0066cc; font-weight: 600; border-bottom: 1px solid #0066cc; padding-bottom: 0.03in;">
      Injury & Treatment Details
    </h3>
    <table style="width: 100%; font-size: 9pt; border-collapse: collapse;">
      <tr>
        <td style="width: 50%; vertical-align: top; padding-right: 0.1in;">
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #dc3545; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Body Part Injured:</strong><br>
            <span style="color: #212529; font-size: 10pt; font-weight: 600;">${data.bodyPartInjured || 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #dc3545; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Type of Injury:</strong><br>
            <span style="color: #212529; font-size: 10pt; font-weight: 600;">${data.typeOfInjury || 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #dc3545;">
            <strong style="color: #495057;">Treatment Location:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.treatmentLocation || 'Not specified'}</span>
          </div>
        </td>
        <td style="width: 50%; vertical-align: top; padding-left: 0.1in;">
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #ffc107; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Type of Medical Care:</strong><br>
            <span style="color: #212529; font-size: 10pt; font-weight: 600;">${data.typeOfMedicalCare ? TYPE_OF_MEDICAL_CARE_MAP[data.typeOfMedicalCare] : 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #ffc107;">
            <strong style="color: #495057;">Prescribed Medication:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.prescribedMedication ? 'Yes' : 'No'}</span>
          </div>
        </td>
      </tr>
    </table>
  `;
  printDiv.appendChild(injurySection);

  // OSHA Classification Section
  const oshaSection = document.createElement('div');
  oshaSection.style.marginBottom = '0.15in';
  oshaSection.innerHTML = `
    <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #0066cc; font-weight: 600; border-bottom: 1px solid #0066cc; padding-bottom: 0.03in;">
      OSHA Classification & Impact
    </h3>
    <table style="width: 100%; border-collapse: collapse; font-size: 9pt; border: 1px solid #dee2e6;">
      <thead>
        <tr style="background: #0066cc; color: white;">
          <th style="padding: 0.08in; text-align: left; font-weight: 600; border-right: 1px solid #004499;">Classification</th>
          <th style="padding: 0.08in; text-align: center; font-weight: 600; width: 20%;">Status</th>
        </tr>
      </thead>
      <tbody>
        <tr style="background: #fff5f5;">
          <td style="padding: 0.06in; border-right: 1px solid #dee2e6; border-bottom: 1px solid #dee2e6;">
            <strong>OSHA Case Type</strong><br>
            <span style="font-size: 8pt; color: #666;">${data.type ? OSHA_TYPE_MAP[data.type] : 'Not specified'}</span>
          </td>
          <td style="padding: 0.06in; text-align: center; font-weight: 700; font-size: 12pt; color: #0066cc; border-bottom: 1px solid #dee2e6;">
            ${data.type ? OSHA_TYPE_MAP[data.type] : 'N/A'}
          </td>
        </tr>
        <tr style="background: #fff8e1;">
          <td style="padding: 0.06in; border-right: 1px solid #dee2e6; border-bottom: 1px solid #dee2e6;">
            <strong>Fatality</strong><br>
            <span style="font-size: 8pt; color: #666;">Employee death from work-related injury</span>
          </td>
          <td style="padding: 0.06in; text-align: center; font-weight: 700; font-size: 12pt; color: ${data.wasDeceased ? '#dc3545' : '#28a745'}; border-bottom: 1px solid #dee2e6;">
            ${data.wasDeceased ? 'Yes' : 'No'}
          </td>
        </tr>
        <tr style="background: #e8f4fd;">
          <td style="padding: 0.06in; border-right: 1px solid #dee2e6; border-bottom: 1px solid #dee2e6;">
            <strong>Hospitalization</strong><br>
            <span style="font-size: 8pt; color: #666;">Employee required overnight hospital stay</span>
          </td>
          <td style="padding: 0.06in; text-align: center; font-weight: 700; font-size: 12pt; color: ${data.wasHospitalized ? '#fd7e14' : '#28a745'}; border-bottom: 1px solid #dee2e6;">
            ${data.wasHospitalized ? 'Yes' : 'No'}
          </td>
        </tr>
        <tr style="background: #f0f8ff;">
          <td style="padding: 0.06in; border-right: 1px solid #dee2e6; border-bottom: 1px solid #dee2e6;">
            <strong>Days Away from Work</strong><br>
            <span style="font-size: 8pt; color: #666;">Number of days employee was unable to work</span>
          </td>
          <td style="padding: 0.06in; text-align: center; font-weight: 700; font-size: 12pt; color: ${data.daysAwayFromWork > 0 ? '#fd7e14' : '#28a745'}; border-bottom: 1px solid #dee2e6;">
            ${data.daysAwayFromWork || 0}
          </td>
        </tr>
        <tr style="background: #f8f9fa;">
          <td style="padding: 0.06in; border-right: 1px solid #dee2e6;">
            <strong>Days of Job Restriction</strong><br>
            <span style="font-size: 8pt; color: #666;">Number of days employee had work restrictions</span>
          </td>
          <td style="padding: 0.06in; text-align: center; font-weight: 700; font-size: 12pt; color: ${data.daysRestrictedFromWork > 0 ? '#fd7e14' : '#28a745'};">
            ${data.daysRestrictedFromWork || 0}
          </td>
        </tr>
      </tbody>
    </table>
  `;
  printDiv.appendChild(oshaSection);

  // Investigation & Actions Section
  const investigationSection = document.createElement('div');
  investigationSection.style.marginBottom = '0.15in';
  investigationSection.style.background = '#f8f9fa';
  investigationSection.style.border = '1px solid #dee2e6';
  investigationSection.style.borderRadius = '4px';
  investigationSection.style.padding = '0.1in';
  investigationSection.innerHTML = `
    <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #0066cc; font-weight: 600; border-bottom: 1px solid #0066cc; padding-bottom: 0.03in;">
      Investigation & Corrective Actions
    </h3>
    ${
      data.witnesses
        ? `
      <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #17a2b8; margin-bottom: 0.05in;">
        <strong style="color: #495057;">Witnesses:</strong><br>
        <span style="color: #212529; font-size: 9pt;">${data.witnesses}</span>
      </div>
    `
        : ''
    }
    ${
      data.rootCauseAnalysis
        ? `
      <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #28a745; margin-bottom: 0.05in;">
        <strong style="color: #495057;">Root Cause Analysis:</strong><br>
        <span style="color: #212529; font-size: 9pt; line-height: 1.4;">${data.rootCauseAnalysis}</span>
      </div>
    `
        : ''
    }
    ${
      data.correctiveActions
        ? `
      <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #6f42c1; margin-bottom: 0.05in;">
        <strong style="color: #495057;">Corrective Actions:</strong><br>
        <span style="color: #212529; font-size: 9pt; line-height: 1.4;">${data.correctiveActions}</span>
      </div>
    `
        : ''
    }
    ${
      data.reasonForReport
        ? `
      <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #fd7e14;">
        <strong style="color: #495057;">Reason for Report:</strong><br>
        <span style="color: #212529; font-size: 9pt; line-height: 1.4;">${data.reasonForReport}</span>
      </div>
    `
        : ''
    }
    ${
      !data.witnesses && !data.rootCauseAnalysis && !data.correctiveActions && !data.reasonForReport
        ? `
      <div style="background: white; padding: 0.08in; border-radius: 3px; border: 1px dashed #dee2e6; text-align: center;">
        <span style="color: #6c757d; font-size: 9pt; font-style: italic;">No additional investigation details provided</span>
      </div>
    `
        : ''
    }
  `;
  printDiv.appendChild(investigationSection);

  // Footer Section
  const footerSection = document.createElement('div');
  footerSection.style.marginTop = '0.2in';
  footerSection.style.paddingTop = '0.1in';
  footerSection.style.borderTop = '1px solid #dee2e6';
  footerSection.style.fontSize = '8pt';
  footerSection.style.color = '#666';
  footerSection.innerHTML = `
    <div style="display: flex; justify-content: space-between; align-items: center;">
      <div>
        <strong>Report Created:</strong> ${formatDate(data.createdAt)}<br>
        ${data.updatedAt ? `<strong>Last Updated:</strong> ${formatDate(data.updatedAt)}` : ''}
      </div>
      <div style="text-align: right;">
        <strong>Form 301 - OSHA Injury and Illness Incident Report</strong><br>
        Generated on ${formatDate(new Date())}
      </div>
    </div>
  `;
  printDiv.appendChild(footerSection);

  // Add to document, print, then remove
  document.body.appendChild(printDiv);

  const originalContents = document.body.innerHTML;
  const originalTitle = document.title;

  // Set a professional title for the PDF
  document.title = `OSHA_Form_301_${data.slug || 'Report'}_${format(new Date(), 'yyyy-MM-dd')}`;

  // Add comprehensive print styles
  const printStyle = document.createElement('style');
  printStyle.innerHTML = `
    @media print {
      @page {
        margin: 0.5in;
        size: letter;
      }
      body {
        margin: 0;
        padding: 0;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
      }
      * {
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
      }
      table {
        page-break-inside: avoid;
      }
      .no-break {
        page-break-inside: avoid;
      }
      /* Force page breaks when needed */
      .page-break-before {
        page-break-before: always;
      }
      .page-break-after {
        page-break-after: always;
      }
      /* Prevent orphan content */
      h3 {
        page-break-after: avoid;
      }
    }
  `;
  document.head.appendChild(printStyle);

  document.body.innerHTML = printDiv.innerHTML;

  setTimeout(() => {
    window.print();
    document.body.innerHTML = originalContents;
    document.title = originalTitle;

    // Remove the added print style
    document.head.removeChild(printStyle);
  }, 500);
};
