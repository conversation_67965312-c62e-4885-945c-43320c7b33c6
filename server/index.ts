/* 
  Note: Instan<PERSON> must be the first module loaded to properly trace all calls. 
  see: https://www.ibm.com/docs/en/instana-observability/current?topic=nodejs-collector-installation#review-common-installation-considerations
  for additional details
*/
import '@server/utils/instana';

import { router as trpcRouter } from '@server/trpc/router';
import { createTrpcContext } from '@server/trpc/trpc';
import { logger } from '@server/utils/logger';
import { createExpressMiddleware } from '@trpc/server/adapters/express';
import express, { Express } from 'express';
import { createHash } from 'crypto';
import { readFile, stat } from 'fs/promises';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';
import ViteExpress from 'vite-express';
import { env } from '../env';
import { shutdown } from './redis/client';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app: Express = express();

app.get('/health', (_, res) => {
  res.status(200).json({
    status: 'ok',
  });
});

app.use(
  '/ehs/trpc',
  createExpressMiddleware({
    router: trpcRouter,
    createContext: createTrpcContext,
    maxBodySize: 10 * 1024 * 1024, // 10MB
  }),
);

// Development mode: ViteExpress will handle serving the client
// Production mode: Serve static files from the dist directory
app.use('/*splat', (_, res, next) => {
  res.set('Content-Security-Policy', `frame-ancestors 'self' ${process.env.UPKEEP_APP_URL};`);
  next();
});

if (env.NODE_ENV === 'production' || env.NODE_ENV === 'staging') {
  app.use(express.static(join(__dirname, '../dist')));

  // Handle all other routes by serving index.html with ETag caching
  let cachedETag: string | null = null;
  let cachedMTime: number | null = null;

  app.get('/*splat', async (req, res) => {
    const htmlPath = join(__dirname, '../dist/index.html');

    try {
      const stats = await stat(htmlPath);
      const mtime = stats.mtime.getTime();

      // Generate ETag if file changed or not cached
      if (cachedMTime !== mtime || !cachedETag) {
        const content = await readFile(htmlPath, 'utf8');
        cachedETag = createHash('md5').update(content).digest('hex');
        cachedMTime = mtime;
      }

      // Check if client has matching ETag
      let clientETag = req.headers['if-none-match'];
      if (Array.isArray(clientETag)) {
        clientETag = clientETag[0];
      }
      clientETag = clientETag?.replace(/^"(.*)"$/, '$1'); // Strip surrounding quotes
      if (clientETag === cachedETag) {
        res.status(304).end();
        return;
      }

      // Send file with ETag for future validation
      res.set('ETag', cachedETag);
      res.set('Cache-Control', 'no-cache'); // Allow caching but require validation
      res.sendFile(htmlPath);
    } catch (error) {
      logger.error('Error serving index.html', { error });
      res.status(500).send('Internal Server Error');
    }
  });
}

ViteExpress.listen(app, env.PORT, () => {
  logger.info('Server started', { port: env.PORT });
});

// Graceful shutdown redis client
process.on('SIGINT', async () => {
  await shutdown();
  process.exit(0);
});
