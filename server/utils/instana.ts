/* c8 ignore start */
import { env } from '../../env';

const { INSTANA_AGENT_HOST, NODE_ENV, INSTANA_ENABLED } = env;

export const isInstanaEnabled: boolean = !!INSTANA_AGENT_HOST && NODE_ENV === 'production' && INSTANA_ENABLED;

const buildInstana = async () => {
  if (isInstanaEnabled) {
    const instanaCollector = await import('@instana/collector');
    return instanaCollector.default();
  }
  return undefined;
};

export const instana = await buildInstana();
/* c8 ignore stop */
